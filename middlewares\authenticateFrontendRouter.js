import AppError from "../utils/appError.js";
import jwt from "jsonwebtoken";
import Department from "../model/department.model.js";
import Distributor from "../model/distributor.model.js";
import catchAsync from "../utils/catchAsync.js";

export const authenticateFrontendRouter = catchAsync(async (req, res, next) => {
  if (!req.headers.auth_token) {
    return next(new AppError("Mising auth_token"));
  }
  const token = req.headers.auth_token;
  if (!token) {
    return next(new AppError("Mising auth_token"));
  }
  const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
  let userId = decodedUser.userId;
  const department = await Department.findById(userId).populate();
  const distributor = await Distributor.findById(userId).populate();
  let user = null;
  if (department) {
    user = department;
  }
  if (distributor) {
    user = distributor;
  }
  if (!user) {
    return res.status(401).json({ error: "You're Not Authorized." });
  }
  res.body = user;
  req.body.current_user = user;
  next();
});
