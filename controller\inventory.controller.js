import Inventory from "../model/inventory.model.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import ExcelJS from "exceljs";
import InventoryUpdateLog from "../model/inventoryLogs.model.js";

export const getInventories = getAll(Inventory);
export const getOneInventory = getOne(Inventory);
export const createInventory = createOne(Inventory);
export const updateInventory = updateOne(Inventory);
export const deleteInventory = deleteOne(Inventory);

// Helper function to merge existing sapSkus with new data
const mergeSapSkus = (existingSapSkus, newSapSkus) => {
  const mergedSapSkus = [...existingSapSkus];

  newSapSkus.forEach((newSapSku) => {
    const index = mergedSapSkus.findIndex(
      (existing) => existing.sku === newSapSku.sku
    );
    if (index > -1) {
      // Update quantity and other fields if needed
      mergedSapSkus[index].quantity = newSapSku.quantity || 0;
      mergedSapSkus[index].eanNo =
        newSapSku.eanNo || mergedSapSkus[index].eanNo;
    } else {
      // Add new sapSku if it doesn't exist
      mergedSapSkus.push(newSapSku);
    }
  });

  return mergedSapSkus;
};

const runChunkedBulkWrite = async (model, bulkOps, chunkSize = 1000) => {
  for (let i = 0; i < bulkOps.length; i += chunkSize) {
    const chunk = bulkOps.slice(i, i + chunkSize);
    const update = await model.bulkWrite(chunk);
    console.log(update);
  }
};

const processInventoryExcel = async (file) => {
  try {
    const sap_skus = [];
    const invalidRows = [];
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(file.buffer);

    const sheet = workbook.getWorksheet(1);

    const ibdSkus = [];
    const materialSkus = [];
    const allRows = [];

    // Single pass to collect SKUs and row data
    for (let i = 2; i <= sheet.rowCount; i++) {
      const row = sheet.getRow(i);
      const materialSku = row.getCell(1).value;
      const ibdSku = row.getCell(2).value;
      const eanNo = row.getCell(3).value;
      const quantity = row.getCell(4).value;

      // Skip completely blank rows
      if (!materialSku && !ibdSku && !eanNo && !quantity) continue;

      if (materialSku) materialSkus.push(materialSku);
      if (ibdSku) ibdSkus.push(ibdSku);

      allRows.push({ materialSku, ibdSku, eanNo, quantity });
    }

    // Fetch inventories
    const inventoriesWithSapSkus = await Inventory.find({
      "sapSkus.sku": { $in: materialSkus },
    }).select("sapSkus sku");

    const sapSkuLookup = {};
    for (const inv of inventoriesWithSapSkus) {
      for (const sapSkuObj of inv.sapSkus) {
        if (!sapSkuLookup[sapSkuObj.sku]) {
          sapSkuLookup[sapSkuObj.sku] = [];
        }
        sapSkuLookup[sapSkuObj.sku].push(inv);
      }
    }

    const inventories = await Inventory.find({
      sku: { $in: ibdSkus },
    }).select("sapSkus sku");

    const inventoryMap = Object.fromEntries(
      inventories.map((inventory) => [inventory.sku, inventory])
    );

    const bulkOps = [];
    const skuAggregationMap = {};

    // Validation and aggregation
    for (const row of allRows) {
      const { materialSku: sku, ibdSku, eanNo, quantity } = row;

      if (!sku || !ibdSku) {
        invalidRows.push({
          sku,
          ibdSku,
          eanNo,
          quantity,
          reason: "Missing data",
        });
        continue;
      }

      const existingInventory = inventoryMap[ibdSku];
      if (!existingInventory) {
        invalidRows.push({
          sku,
          ibdSku,
          eanNo,
          quantity,
          reason: "Base product missing",
        });
        continue;
      }

      const conflictingInventories = sapSkuLookup[sku] || [];
      const conflict = conflictingInventories.find((inv) => inv.sku !== ibdSku);

      if (conflict) {
        invalidRows.push({
          sku,
          ibdSku,
          eanNo,
          quantity,
          reason: `Sap Sku already exist ${conflict.sku}`,
        });
        continue;
      }

      const newSapSku = { sku, eanNo, quantity };
      if (!skuAggregationMap[ibdSku]) {
        skuAggregationMap[ibdSku] = existingInventory.sapSkus || [];
      }

      sap_skus.push(newSapSku);
      skuAggregationMap[ibdSku] = mergeSapSkus(skuAggregationMap[ibdSku], [
        newSapSku,
      ]);
    }

    // Create bulk update operations
    for (const [ibdSku, aggregatedSapSkus] of Object.entries(
      skuAggregationMap
    )) {
      bulkOps.push({
        updateOne: {
          filter: { sku: ibdSku },
          update: { $set: { sapSkus: aggregatedSapSkus } },
        },
      });
    }

    // Finds all the sapSkus ids which are present in the system but not in the excel sheet.
    const materialSkuSet = new Set(materialSkus);

    const allInventories = await Inventory.find({
      "sapSkus.sku": { $exists: true },
    }).select("sapSkus");

    const inventoryUpdateOps = [];
    for (const inventory of allInventories) {
      for (const s of inventory.sapSkus) {
        const plain = s.toObject ? s.toObject() : s;
        if (!materialSkuSet.has(plain.sku) && plain.quantity !== 0) {
          inventoryUpdateOps.push({
            updateOne: {
              filter: { "sapSkus._id": plain._id },
              update: { $set: { "sapSkus.$[elem].quantity": 0 } },
              arrayFilters: [{ "elem._id": plain._id }],
            },
          });
        }
      }
    }

    // Execute bulk operations for sapSkus update
    if (bulkOps.length > 0) {
      await runChunkedBulkWrite(Inventory, bulkOps);
    }

    if (invalidRows.length > 0) {
      const newWorkbook = new ExcelJS.Workbook();
      const newSheet = newWorkbook.addWorksheet("Invalid Rows");
      newSheet.addRow([
        "Material (SKU)",
        "IBD SKU",
        "EAN No.",
        "Stock",
        "Reason",
      ]);

      invalidRows.forEach((row) => {
        newSheet.addRow([
          row.sku || "N/A",
          row.ibdSku || "N/A",
          row.eanNo || "N/A",
          row.quantity || "N/A",
          row.reason,
        ]);
      });

      const buffer = await newWorkbook.xlsx.writeBuffer();
      return {
        sap_skus,
        invalidFile: buffer,
        skuCount: sheet.rowCount - 1, // skuCount for logs
        allRows, // for inventory update logs
        inventoryUpdateOps, // to update inventory
      };
    }

    return {
      sap_skus,
      skuCount: sheet.rowCount-1,
      allRows,
      inventoryUpdateOps, // to update inventory
    };
  } catch (error) {
    console.error("Error processing Excel file:", error);
    throw new Error("Failed to process Excel file");
  }
};

export const handleInventoryExcel = async (req, res) => {
  try {
    const file = req.file;

    const inventoryLog = {
      fileName: file.originalname,
      uploadTime: new Date().toISOString(),
      uploadedBy: req?.user?.name ?? "Placeholder Name",
      skus: 0,
    };

    const { sap_skus, invalidFile, skuCount, allRows, inventoryUpdateOps } =
      await processInventoryExcel(file);

    // updating logs for this upload
    inventoryLog.skus = skuCount;
    inventoryLog.uploadedSkus = allRows;
    InventoryUpdateLog.create(inventoryLog);

    // updating inventory based on excel sheet uploaded
    runChunkedBulkWrite(Inventory, inventoryUpdateOps);

    if (invalidFile) {
      // If there are invalid rows, send the Excel file along with the response
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=InvalidRows.xlsx"
      );
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );

      return res.status(200).json({
        success: true,
        message: "Inventory updated successfully. Some rows had issues.",
        data: sap_skus,
        file: invalidFile.toString("base64"), // Convert the buffer to a base64 string
      });
    }

    // If no invalid rows, send a standard response
    return res.status(200).json({
      success: true,
      message: "Inventory updated successfully",
      data: sap_skus,
    });
  } catch (error) {
    console.error("Error in file upload route: ", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update inventory",
      error: error.message,
    });
  }
};

export const getAllInventories = async (req, res) => {
  try {
    // Get page and limit from query parameters (default to page 1, limit 10 if not provided)
    const page = parseInt(req.query.page) || 1; // Default to page 1
    let limit = parseInt(req.query.limit) || 50; // Default to 50 items per page
    limit = limit > 50 ? 50 : limit;

    // Calculate the number of items to skip based on the page and limit
    const skip = (page - 1) * limit;

    // Get the search query from the request
    const searchQuery = req.query.code || ""; // Default to an empty string if no search query is provided

    // Create a search condition that matches inventory.sku or sapSku.sku only if 'code' is provided
    const searchCondition = searchQuery
      ? {
          $or: [
            { sku: { $regex: searchQuery, $options: "i" } }, // Case-insensitive search on inventory.sku
            { "sapSkus.sku": { $regex: searchQuery, $options: "i" } }, // Case-insensitive search on sapSkus.sku
          ],
        }
      : {}; // If no search query, no filtering condition (fetch all)

    // Fetch inventory documents with pagination and search condition
    const inventories = await Inventory.find(searchCondition)
      .skip(skip)
      .limit(limit);

    // Get total count of documents for pagination info (with the same search condition)
    const totalDocuments = await Inventory.countDocuments(searchCondition);

    // Format the result
    const mappedInventories = inventories.flatMap((inventory) =>
      inventory?.sapSkus?.length
        ? inventory.sapSkus.map((sapSku) => ({
            ibd_sku: inventory.sku, // ISBD SKU from the inventory
            sap_sku: sapSku.sku, // SAP SKU from sapSkus array
            quantity: sapSku.quantity, // Quantity from sapSkus array
            ean_no: sapSku.eanNo, // EAN No. from sapSkus array
            image: inventory.image,
          }))
        : null
    );

    const formattedInventories = mappedInventories.filter(
      (item) => item !== null
    );

    // Return the formatted data along with pagination metadata
    res.status(200).json({
      totalItems: totalDocuments,
      currentPage: page,
      totalPages: Math.ceil(totalDocuments / limit),
      pageSize: formattedInventories.length,
      data: formattedInventories,
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "Server error", error });
  }
};

export const createAndSendExcel = async (req, res) => {
  try {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Inventory Data");

    // Add header row
    worksheet.addRow(["Material", "IBD SKU", "EAN No.", "Stock"]);

    // Add data rows
    const data = [
      ["NS1595NL03", "1595NL03", "8905906629930", 40],
      ["NR1595NL04", "1595NL03", "8905631842673", 20],
    ];

    data.forEach((row) => {
      worksheet.addRow(row);
    });

    // Adjust column widths
    worksheet.columns = [
      { header: "Material", key: "material", width: 20 },
      { header: "IBD SKU", key: "ibd_sku", width: 15 },
      { header: "EAN No.", key: "ean_no", width: 20 },
      { header: "Stock", key: "stock", width: 10 },
    ];

    // Set the file name
    const fileName = "Inventory_Data.xlsx";

    // Write the workbook to a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Set response headers to indicate a file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);

    // Send the buffer as the response
    res.send(buffer);
  } catch (error) {
    console.error("Error generating Excel file:", error);
    res.status(500).send("Failed to create Excel file");
  }
};
