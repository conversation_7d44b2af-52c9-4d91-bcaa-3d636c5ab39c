//! Any function without arguments that means there is an issue with design

import DistributorSchema from "../../model/distributor/createDistributor.model.js";
import { v4 as uuidv4 } from 'uuid';

// Create Distributor
export async function createDistributorEntry(distributor){
  const newDistributorEntryInDb = await createDistributorEntryInDb(distributor);
  const createDistributorInShopify = await createDistributorEntryInShopify(distributor);
  
  return createDistributorInShopify;
}

// Create distributor entry in db
export async function createDistributorEntryInDb(distributor){
  const uniqId = uuidv4(); 
  const distributorSchema = DistributorSchema.create({
    id: `${uniqId}`,
    ...distributor
  })
  return distributorSchema;
}

// create distributior entry in shopify
export async function createDistributorEntryInShopify(distributor){
  const distributorSince = distributor.customerSince;
  const distributorExternalId = distributor.externalId;
  const distributorName = distributor.name;
  const distributorNotes = distributor.note;

  const companyEmail = distributor.companyEmail;
  const companyFirstName = distributor.companyFirstName;
  const companyLastName = distributor.companyLastName;
  const companyPhone = distributor.companyPhone;
  const companyLocale = distributor.companyLocale;

  const myHeaders = new Headers();
  myHeaders.append("x-shopify-access-token", process.env.SHOPIFY_ADMIN_ACCESS_TOKEN);
  myHeaders.append("Content-Type", "application/json");

  const graphql = JSON.stringify({
    query: `mutation MyMutation {\r\n  companyCreate(\r\n    input: {company: {customerSince: \"${distributorSince}\", externalId: \"${distributorExternalId}\", name: \"${distributorName}\", note: \"${distributorNotes}\"}},\r\n  
       companyContact: {email: \"${companyEmail}\", firstName: \"${companyFirstName}\", lastName: \"${companyLastName}\", phone: \"${companyPhone}\", locale: \"${companyLocale}\"}\r\n      ) {\r\n    company {\r\n      id\r\n      name\r\n    }\r\n    userErrors {\r\n      code\r\n      field\r\n      message\r\n    }\r\n  }\r\n}\r\n`,
    variables: {}
  })
  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: graphql,
    redirect: "follow"
  };

  const responsePromise = await fetch(`${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`, requestOptions)
  const response = await responsePromise.json();
  return response;
}

export async function changeDistributorInfo(modifiedDistributorInfo){
  const updateDistributorInfoInDB = await changeDistributorInfoInDB(modifiedDistributorInfo);
  const updateDistributorInfoInShopify = await changeDistributorInfoInShopify(modifiedDistributorInfo);

  return updateDistributorInfoInShopify;
}

// modify distributor details in shopify
export async function changeDistributorInfoInShopify(modifiedDistributorInfo){
  const companyId = modifiedDistributorInfo.companyId;
  const externalId = modifiedDistributorInfo.externalId;
  const name = modifiedDistributorInfo.name;
  const note = modifiedDistributorInfo.note;

  const myHeaders = new Headers();
  myHeaders.append("x-shopify-access-token", process.env.SHOPIFY_ADMIN_ACCESS_TOKEN);
  myHeaders.append("Content-Type", "application/json");
  myHeaders.append("Cookie", "_shopify_y=ff569c80-7884-4bae-a2c2-16a369c8e0a5; _y=ff569c80-7884-4bae-a2c2-16a369c8e0a5; localization=SA; secure_customer_sig=");

  const graphql = JSON.stringify({
    query: `mutation MyMutation {\r\n  companyUpdate(\r\n    companyId: \"${companyId}\"\r\n    input: {externalId: \"${externalId}\", name: \"${name}\", note: \"${note}\"}\r\n  ) {\r\n    company {\r\n      name\r\n      updatedAt\r\n    }\r\n    userErrors {\r\n      code\r\n      field\r\n      message\r\n    }\r\n  }\r\n}\r\n`,
    variables: {}
  })
  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: graphql,
    redirect: "follow"
  };

  const responsePromise = await fetch(`${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`, requestOptions)
  const response = await responsePromise.json();

  return response;
}

// modify distributior details in db
export async function changeDistributorInfoInDB(modifiedDistributorInfo){
  const updateDistributorInfo = DistributorSchema.findOneAndUpdate(
    {id: `${modifiedDistributorInfo.id}`},
    {$set:{
      ...modifiedDistributorInfo
    }},
  )
  return updateDistributorInfo;
}

// delete distributor details
export async function deleteDistributorEntry(distributorData){
  const deleteDistributorDataFromDB = await deleteDistributorEntryFromDB(distributorData);
  const deleteDistributorDataFromShopify = await deleteDistributorEntryFromShopify(distributorData);

  return deleteDistributorDataFromShopify;
}

// delete distributor details
export async function deleteDistributorEntryFromShopify(distributorData){

  const { companyId } = distributorData;

  const myHeaders = new Headers();
  myHeaders.append("x-shopify-access-token", process.env.SHOPIFY_ADMIN_ACCESS_TOKEN);
  myHeaders.append("Content-Type", "application/json");
  myHeaders.append("Cookie", "_shopify_y=ff569c80-7884-4bae-a2c2-16a369c8e0a5; _y=ff569c80-7884-4bae-a2c2-16a369c8e0a5; localization=SA; secure_customer_sig=");

  const graphql = JSON.stringify({
    query: `mutation MyMutation {\r\n  companyDelete(id: \"${companyId}\") {\r\n    deletedCompanyId\r\n    userErrors {\r\n      code\r\n      field\r\n      message\r\n    }\r\n  }\r\n}\r\n`,
    variables: {}
  })
  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: graphql,
    redirect: "follow"
  };

  const responsePromise = await fetch(`${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`, requestOptions)
  const response = await responsePromise.json();

  return response;
}

// delete distributor details
export async function deleteDistributorEntryFromDB(distributorData){
  const { id } = distributorData;
  const deleteDistributorData = await DistributorSchema.deleteOne({ id });

  return deleteDistributorData;
}