import mongoose from "mongoose";

const emailNotificationSchema = new mongoose.Schema(
  {
    emailCategory: {
      type: String,
    },
    emailType: {
      type: String,
      enum: [
        "ORDER_CREATE",
        "SHIPMENT_CREATE",
        "SHIPMENT_STATUS_CHANGE",
        "SHIPMENT_ESCALATION",
        "CUSTOMER_ACCOUNT_ACTIVATION",
        "PI_GENERATED",
        "ORDER_CREATE_NOTIFY_MANAGER",
        "SLA_BREACH",
        "LINE_SHEET",
      ],
    },
    isProcessed: {
      type: Boolean,
      default: false,
    },
    reciepient: {
      required: true,
      type: Object,
      name: {
        type: String,
      },
      email: {
        type: String,
      },
    },
    cc: {
      type: Array,
    },
    targetPugFile: {
      type: String,
    },
    emailPayload: {
      type: Object,
    },
    note: {
      type: String,
    },
    tag: {
      type: String,
    },
  },
  { timestamps: true }
);

const EmailNotification = mongoose.model(
  "EmailNotification",
  emailNotificationSchema
);
export default EmailNotification;
