import mongoose from "mongoose";
import Distributor from "./distributor.model.js";
import { UUID } from "mongodb";

const orderSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      unique: true,
    },
    productCategory: {
      type: String,
    },
    current_subtotal_price: {
      type: String,
    },
    totalQuantity: {
      type: String,
    },
    shopifyCompanyId: {
      type: Number,
      required: true,
    },
    shopifyCompanyLocationId: {
      type: Number,
    },
    cartId: {
      type: String,
    },
    status: {
      type: mongoose.Schema.ObjectId,
      ref: "Status",
      required: true,
    },
    distributor: {
      type: mongoose.Schema.ObjectId,
      ref: "Distributor",
      // required: true,
    },
    financial_status: {
      type: String,
    },
    customer: {
      type: Object,
    },
    // customer: {
    //   type: Object,
    //   shopifyCustomerId: {
    //     type: Number,
    //   },
    //   name: {
    //     type: String,
    //   },
    //   email: {
    //     type: String,
    //   },
    //   phone: {
    //     type: String,
    //   },
    // },
    line_items: {
      type: Array,
    },
    created_at: {
      type: String,
    },
    sku: {
      type: String,
    },
    shipping_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    billing_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    attributes: [
      {
        attributeId: {
          type: String,
          required: true,
          default: new UUID(),
        },
        name: {
          type: String,
        },
        type: {
          type: String,
        },
        value: {
          type: Array,
        },
      },
    ],
    timeline: {
      type: Array,
      default: [
        {
          time: new Date(),
          comment: "Order created",
        },
      ],
    },
  },
  { timestamps: true }
);

orderSchema.pre(/^find/, function (next) {
  this.populate({
    path: "status",
    select: "status pseudoId colorCode",
  })
    .populate({
      path: "distributor",
      select: "name shopifyCompanyId email phone priority country",
    })
    .populate({
      path: "shipping_address",
      // select: "name shopifyCompanyId email priority",
    })
    .populate({
      path: "billing_address",
      // select: "name shopifyCompanyId email priority",
    })
    .lean();
  next();
});

orderSchema.pre("save", async function (next) {
  const latestOrder = await Order.find({}).sort({ _id: -1 }).limit(1);
  const newOrderName =
    latestOrder[0]?.name !== undefined ? latestOrder[0]?.name * 1 + 1 : 1000;
  this.name = newOrderName;
  next();
});

const Order = mongoose.model("Order", orderSchema);
export default Order;
