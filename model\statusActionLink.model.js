import mongoose from "mongoose";

const formDataSchema = new mongoose.Schema({
  input_name: {
    type: String,
    required: true,
  },
  input_type: {
    type: String,
    required: true,
  },
  value_type: {
    type: String,
    required: true,
  },
  input_box: {
    type: String,
    required: true,
    enum: ["text", "number", "file", "boolean", "date", "checkbox", "tel", "time"],
  },
  is_mandatory: {
    type: Boolean,
    required: true,
  },
  priceValidation: {
    type: Boolean,
    default: false,
  },
});

const statusActionLinkSchema = new mongoose.Schema({
  action_id: {
    type: mongoose.Schema.ObjectId,
    required: true,
  },
  action_display_name: {
    type: String,
    required: true,
  },
  current_status_id: {
    type: mongoose.Schema.ObjectId,
    required: true,
  },
  next_status_id: {
    type: mongoose.Schema.ObjectId,
    required: true,
  },
  formData: {
    type: [formDataSchema],
  },
  enabled: {
    type: Boolean,
    default: false,
  },
});

// statusActionLinkSchema.pre(/^find/, function (next) {
//   this.populate({
//     path: "status",
//     // select: "status pseudoId colorCode",
//   })
//     .populate({
//       path: "order",
//       // select: "name",
//     })
//     .lean();
//   next();
// });

const StatusActionLink = mongoose.model(
  "StatusActionLink",
  statusActionLinkSchema
);

export default StatusActionLink;

const abc = {
  current_status: {
    name: "Payment pending",
    id: "fcwsneuin32nsa",
  },
  actions: [
    {
      action_id: "234refw43r35",
      action_display_name: "Approve Payment",
      next_status: {
        name: "Allocated to warehouse",
        id: "fcwsneuin32nsa",
      },
      formData: [
        {
          input_name: "transaction id",
          input_type: "input",
          value_type: "number",
          is_mandatory: true,
        },
      ],
    },
    {
      action_id: "234refwdfsr3q43r35",
      action_display_name: "Reject Payment",
      next_status: {
        name: "Change_requested",
        id: "23v234fn32nsa",
      },
      formData: [
        {
          input_name: "Reason",
          input_type: "input",
          value_type: "text",
          is_mandatory: true,
        },
      ],
    },
    {
      action_id: "12323242e3d",
      action_display_name: "Cancel Shipment",
      next_status: {
        name: "Cancelled",
        id: "wrwewf435rvc",
      },
      formData: [
        {
          input_name: "Reason",
          input_type: "input",
          value_type: "text",
          is_mandatory: true,
        },
      ],
    },
  ],
};
