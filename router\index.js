import express from "express";
import * as dotenv from "dotenv";
dotenv.config();

import DepartmentRouter from "./department.router.js";
import DistributorRouter from "./distributor.router.js";
import AddressRouter from "./address.router.js";
import CompanyRouter from "./company.router.js";
import OrganizationRouter from "./organization.router.js";
import DesignationRouter from "./designation.router.js";
import StatusRouter from "./status.router.js";
import TimelineRouter from "./timeline.router.js";
import CartRouter from "./cart.router.js";
import DepartmentTypeRouter from "./departmentType.router.js";
import OrderRouter from "./order.router.js";
import ShopifyRouter from "./shopify.router.js";
import ShipmentRouter from "./shipment.router.js";
import UserGroupRouter from "./usergroup.router.js";
import UserRouter from "./user.router.js";
import ActionRouter from "./action.router.js";
import StatusActionLinkRouter from "./statusActionLink.router.js";
import LineSheetRouter from "./linesheet.router.js";
import reportRouter from "./report.router.js";
import PmrStatusRouter from "./pmrStatus.router.js";
import ShipmentsAlignedRouter from "./shipmentAligned.router.js";
import ShipmentsSummaryRouter from "./shipmentsSummary.router.js";
import SearchRouter from "./search.router.js";
import ShipmentSearchRouter from "./shipmentSearch.router.js";
import AddToCartRouter from "./addToCartRouter.js";
import FrontendAuthRouter from "./authenticate.router.js";
import InventoryRouter from "./inventory.router.js";
import SampleShipmentRouter from "./sampleSheet.router.js";
import frontendOrderRouter from "./frontendOrder.router.js";

import { authenticateFrontendRouter } from "../middlewares/authenticateFrontendRouter.js";

const router = express.Router();

const getDistributorList = async (req, res) => {
  const currentUser = req.body.current_user;
  const isCountryManager = currentUser?.designation?.isCountryManager;
  if (isCountryManager) {
  }
  res.status(200).json({ user: currentUser });
};

router.use("/frontend/cart", authenticateFrontendRouter, CartRouter);
router.use("/frontend/addToCart", authenticateFrontendRouter, AddToCartRouter);
router.use("/frontend/order", frontendOrderRouter);
router.use(
  "/frontend/auth_data",
  authenticateFrontendRouter,
  getDistributorList
);

router.use("/frontend/address", authenticateFrontendRouter, AddressRouter);

router.use("/department_type", DepartmentTypeRouter);
router.use("/department_people", DepartmentRouter);
router.use("/timeline", TimelineRouter);
router.use("/distributor", DistributorRouter);
router.use("/organization", OrganizationRouter);
router.use("/status", StatusRouter);
router.use("/shipment", ShipmentRouter);
router.use("/shopify", ShopifyRouter);
router.use("/order", OrderRouter);
router.use("/cart", CartRouter);
router.use("/designation", DesignationRouter);
router.use("/usergroup", UserGroupRouter);
router.use("/user", UserRouter);
router.use("/action", ActionRouter);
router.use("/status_action_link", StatusActionLinkRouter);
router.use("/linesheet", LineSheetRouter);
router.use("/company", CompanyRouter);
router.use("/report", reportRouter);
router.use("/pmrStatus", PmrStatusRouter);
router.use("/shipmentsAligned", ShipmentsAlignedRouter);
router.use("/shipmentsSummary", ShipmentsSummaryRouter);
router.use("/search", SearchRouter);
router.use("/shipmentSearch", ShipmentSearchRouter);
router.use("/addToCart", AddToCartRouter);
router.use("/frontend/auth", FrontendAuthRouter);
router.use("/inventory", InventoryRouter);
router.use("/sampleSheet", SampleShipmentRouter);

export default router;
