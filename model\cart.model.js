import mongoose from "mongoose";
import Distributor from "./distributor.model.js";
import { UUID } from "mongodb";

const cartSchema = new mongoose.Schema(
  {
    totalQuantity: {
      type: String,
    },
    
    shopifyCompanyId: {
      type: Number,
      required: true,
    },
    // shopifyCompanyLocationId: {
    //   type: Number,
    // },
    description: {
      type: String,
    },
    status: {
      type: String,
      enum: ["CREATED", "COMPLETED", "ABANDONED"],
      default: "CREATED",
    },
    distributor: {
      type: mongoose.Schema.ObjectId,
      ref: "Distributor",
      required: true,
    },
    customer: {
      type: Object,
      shopifyCustomerId: {
        type: Number,
      },
      name: {
        type: String,
      },
      email: {
        type: String,
      },
      phone: {
        type: String,
      },
    },
    shipping_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    billing_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    uploadedSheetName: {
      type: String,
    },
    uploadedSheetPath: {
      type: String,
    },
    line_items: [
      {
        shopifyVariantId: {
          type: String,
        },
        shopifyProductId: {
          type: String,
        },
        productTitle: {
          type: String,
        },
        variantTitle: {
          type: String,
        },
        image: {
          type: String,
        },
        quantity: {
          type: Number,
        },
        price: {
          type: Number,
        },
        sku: {
          type: String,
        },
        productCategory: {
          type: String,
        },
        cluster: {
          type: String,
        },
        caseMaterial: {
          type: String,
        },
        caseWidth: {
          type: String,
        },
        caseLength: {
          type: String,
        },
        caseThickness: {
          type: String,
        },
        strapMaterial: {
          type: String,
        },
        dialColor: {
          type: String,
        },
        function: {
          type: String,
        },
        pmrType: {
          type: String,
        },
        brand: {
          type: String,
        },
        gender: {
          type: String,
        },
        collection: {
          type: String,
        },
      },
    ],
  },
  { timestamps: true }
);

cartSchema.pre(/^find/, function (next) {
  this.populate({
    path: "distributor",
    select: "name shopifyCompanyId email priority",
  }).populate({
    path: "shipping_address",
    // select: "name shopifyCompanyId email priority",
  }).populate({
    path: "billing_address",
    // select: "name shopifyCompanyId email priority",
  }).lean();
  next();
});

const Cart = mongoose.model("Cart", cartSchema);
export default Cart;