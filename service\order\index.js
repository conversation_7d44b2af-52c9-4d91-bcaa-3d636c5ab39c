import Order from "../../model/order.model.js";

export const getTotalInventoryAvailable = async (variantIds) => {
  const myHeaders = new Headers();
  myHeaders.append(
    "X-Shopify-Storefront-Access-Token",
    process.env.SHOPIFY_STOREFRONT_ACCESS_TOKEN
  );
  myHeaders.append("Content-Type", "application/json");

  const graphql = JSON.stringify({
    query: `{\r\n  nodes(ids: ${JSON.stringify(
      variantIds
    )}) {\r\n    ... on ProductVariant {\r\n      id\r\n      quantityAvailable\r\n    }\r\n  }\r\n}\r\n`,
    variables: {},
  });

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: graphql,
    redirect: "follow",
  };

  const resultPromise = await fetch(
    `${process.env.SHOP_URL}/api/${process.env.VERSION}/graphql.json`,
    requestOptions
  );
  const result = await resultPromise.json();

  return result;
};

// export const createOrderWithAvailableQuantity = async () => {
//   const myHeaders = new Headers();
//   myHeaders.append("Content-Type", "application/json");
//   myHeaders.append("X-Shopify-Access-Token", process.env.SHOPIFY_ADMIN_ACCESS_TOKEN);
//   const requestOptions = {
//     method: "POST",
//     headers: myHeaders,
//     body: raw,
//     redirect: "follow"
//   };

//   const resultPromise = await fetch(`${process.env.SHOP_URL}/api/createOrder`, requestOptions);
//   const finalResult = await resultPromise.json();

//   return finalResult;
// }

export const extractAllTheOrderIds = async (pastDate, currentDate) => {
  const ids = await Order.distinct("_id", {
    createdAt: {
      $gte: pastDate,
      $lte: currentDate,
    },
  });
  return ids;
};
