import express from "express";
import multer from "multer";
import {
  createAndSendExcel,
  getAllInventories,
  handleInventoryExcel,
} from "../controller/inventory.controller.js";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";

const inventoryRouter = express.Router();

const storage = multer.memoryStorage();
const upload = multer({ storage });

inventoryRouter.post(
  "/upload-inventory",
  authenticateDepttPeopleAccess("inventory", "write"),
  upload.single("file"),
  handleInventoryExcel
);
inventoryRouter.get(
  "/inventories",
  authenticateDepttPeopleAccess("inventory", "read"),
  getAllInventories
);
inventoryRouter.get(
  "/stock-sample-file",
  authenticateDepttPeopleAccess("inventory", "read"),
  createAndSendExcel
);

export default inventoryRouter;
