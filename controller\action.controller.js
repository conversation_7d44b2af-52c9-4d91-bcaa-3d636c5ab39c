import StatusActionLink from "../model/statusActionLink.model.js";
import Action from "../model/action.model.js";
import Shipment from "../model/shipment.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import Inventory from "../model/inventory.model.js";
import Company from "../model/company.model.js";
import mongoose from "mongoose";

import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import Department from "../model/department.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import fs from "fs";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import {
  extractKeyFromURL,
  formattedMonthAndYear,
  generateSignedURL,
  getCountry,
  uploadToS3,
} from "../utils/helperFunction.js";
import DepartmentType from "../model/departmentType.model.js";
import {
  getDepartmentNotifiedEmails,
  getDepartmentForCreated,
} from "../utils/findNotifiedDepartment.js";
import Distributor from "../model/distributor.model.js";
import PISheet from "../model/piSheet.model.js";
import { month } from "./shipment.controller.js";
import BulkProcessingStatus from "../model/bulkProcessing.model.js";
import { fork } from "child_process";

const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
const __dirname = dirname(__filename);

console.log(" dir Name", __dirname);

export const getAction = getAll(Action);
export const getOneAction = getOne(Action);
export const createAction = createOne(Action);
export const updateAction = updateOne(Action);
export const deleteAction = deleteOne(Action);

export const changeStatusAction = async (req, res) => {
  const { action_link_id, shipment_id, form_data } = req.body;
  const current_user = req.body.current_user || req.user;

  try {
    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }
    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const totalAmount = shipment?.lineItems?.reduce(
      (sum, item) => sum + item.requested * item.price,
      0
    );

    const salesOrderPriceField = statusActionLinkData?.formData?.find(
      (data) => data.priceValidation === true
    );

    if (salesOrderPriceField) {
      let salesOrderPriceValue;
      if (Array.isArray(form_data)) {
        salesOrderPriceValue = form_data.find(
          (item) => item?.input_id === salesOrderPriceField?._id
        );
      } else {
        console.error("form_data is not an array:", form_data);
      }

      const salesOrderPrice = parseFloat(salesOrderPriceValue?.value);

      // Compare salesOrderPrice with totalAmount
      if (Math.abs(totalAmount - salesOrderPrice) >= 1) {
        return res.send({
          error:
            "Difference between actual price and sales order price should be less than 1.",
        });
      }
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();

    const current_user_deptt_ids =
      current_user.departmentType?.map((dept) => dept._id.toString()) || [];

    if (!current_user_deptt_ids.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    //this id is of Allocate inventory action link
    if (action_link_id === "6645e2c10f7a9ff7099cad53") {
      const error = await holdManualAllocatedInventory(shipment.lineItems);
      if (error) {
        return res.send({ error: "Insufficient quantity" });
      }
      const updatedShipment = await Shipment.findByIdAndUpdate(
        shipment._id,
        { "sla.statusEnded": true },
        { new: true }
      );
    }

    const formSchema = statusActionLinkData.formData;

    const valid = validateFormData(formSchema, form_data);

    if (!valid) {
      return res.send({ error: "Form values are missing or invalid" });
    }
    let fileUrl = null;
    if (req.file) {
      const filePath = path.join(
        __dirname,
        "..",
        "asset",
        "upload",
        "shipment",
        req.file.filename
      );

      try {
        const uploadResult = await uploadToS3(
          filePath,
          process.env.AWS_S3_BUCKET,
          req.file.filename
        );
        fileUrl = uploadResult.Location; // Get the S3 URL

        // Optionally delete the local file after upload
        fs.unlinkSync(filePath);
      } catch (err) {
        console.error("S3 Upload Error:", err);
        return res.send({ error: "File upload failed" });
      }
    }

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    const formSchemaLookup = formSchema.reduce((acc, item) => {
      acc[item._id] = item.input_name;
      return acc;
    }, {});

    const processedFormData = JSON.parse(form_data)?.map((item) => {
      const inputName = formSchemaLookup[item.input_id] || item.input_id;
      // Check if the value is empty (null, undefined, empty object, empty string, etc.)
      if (
        !item.value ||
        (typeof item.value === "object" && Object.keys(item.value).length === 0)
      ) {
        return {
          ...item,
          input_name: inputName,
          value: fileUrl,
        };
      }

      return {
        ...item,
        input_name: inputName, // Add the input_name to each item
      };
    });

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });

    const updateStatus = await Shipment.findByIdAndUpdate(shipment_id, {
      $set: {
        status: statusActionLinkData.next_status_id,
      },
      $push: {
        timeline: {
          time: new Date(),
          comment: `Shipment Status has been changed by ${current_user.name} from ${currentStatus.status} to ${nextStatus.status}`,
        },
        attributes: {
          name: shipment.status.pseudoId,
          type: "JSON",
          value: processedFormData,
          time: new Date(),
        },
        status_change_history: {
          status: statusActionLinkData.next_status_id,
        },
      },
    });

    if (action_link_id === "663ba558513a0934f1734d06") {
      //this id is of Approve payment action link
      await consumeOnHoldInventory(shipment.lineItems);
    }

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    /* 
     To generate the dynamic PI number.  
    */

    const orderCreatedAt = shipment.order.createdAt;
    const formattedYearAndMonth = formattedMonthAndYear(orderCreatedAt);
    const distributorCountry = getCountry(distributor);
    const piNumber = `PI - ${shipment.piName}/${distributorCountry}/${formattedYearAndMonth}`;

    /* 
     Supply chain departments people, BM and distributer
    */

    const company = await Company.findOne({
      shopifyCompanyId: distributor.shopifyCompanyId,
    });

    let subject = nextStatus.status;

    /* 
     Mapping of the pseudoIds with the ObjectId of the status
     66fe4f616342b66c2b8cf28a: "DELIVERY_ADVICE"
     6645d804bc12e28864479bf8: "DELIVERY_CREATION_AGAINST_SO"
     663c4b5f864c679bd21974b4: "PAYMENT_PENDING"
     663c4a84864c679bd21974a5: "AWAITING_SAP_PI"
   */

    /* 
      Get the PI sheet details
     
    */

    const existingPiSheet = await PISheet.findOne({
      shipment_name: shipment.name,
    });

    let piDetails;

    if (existingPiSheet) {
      const piSheetCreatedAtDate = existingPiSheet.createdAt;
      const excelSheetUrl = existingPiSheet.excelSheetUrl;
      const pdfSheetUrl = existingPiSheet.pdfSheetUrl;
      const piSerialNumber = existingPiSheet.sNo;
      const totalQuantity = existingPiSheet.totalItems;
      const piId = `PI-${shipment.piName}-${distributorCountry}-${formattedYearAndMonth}`;

      const items = shipment.lineItems.flatMap((lineItem, index) =>
        lineItem.sapSkusUsed.map((sapSku) => ({
          item: index + 1,
          ibd_sku: lineItem.productTitle,
          sap_sku: sapSku.sku,
          quantity: sapSku.quantityTaken || 0, // Use quantityTaken from sapSkusUsed
          price: lineItem.price,
          amountSum: lineItem.price * (sapSku.quantityTaken || 0), // Calculate amount sum
        }))
      );

      const subTotal = items.reduce((sum, item) => sum + item.amountSum, 0);

      const piValue = subTotal;

      piDetails = {
        piSheetCreatedAtDate,
        totalQuantity,
        piId,
        piValue,
        piSerialNumber,
        excelSheetUrl,
        pdfSheetUrl,
      };
    }

    const date = new Date();

    let name = `PI-${piDetails?.piSerialNumber}-${
      distributor?.country?.substring(0, 3).toUpperCase() || "IND"
    }-${month[date.getMonth()]}${date.getFullYear().toString().substr(-2)}`;

    let pugFileName;

    let recipients;
    let emailAttachments = [];

    const ccRecipients = await getDepartmentsPeoples("SECONDARY_NOTIFICATION");

    if (nextStatus._id.toString() === "66fe4f616342b66c2b8cf28a") {
      /* Delivery advice -------------------------------------------------------------------------------->>>>> */

      subject = `Initiate Delivery Advice_${company.name}_${piNumber}`;
      pugFileName = "deliveryAdvice";

      const transformedManagers = await getCountryManagersOfDistributor(
        distributerId
      );

      const tranformedLogisticsPeople = await getDepartmentsPeoples(
        "LOGISTICS_AND_PAYMENTS"
      );

      const tranformedSupplyChainPeople = await getDepartmentsPeoples(
        "SUPPLY_CHAIN_OPERATIONS"
      );

      recipients = onlyUniqueRecipients([
        ...transformedManagers,
        ...tranformedLogisticsPeople,
        ...tranformedSupplyChainPeople,
      ]);
    } else if (nextStatus._id.toString() === "6645d804bc12e28864479bf8") {
      /* Delivery creation against SO -------------------------------------------------------------------------------->>>>> */

      subject = `Titan Order Shipping Docs Created ${company.name}_${piNumber}`;
      pugFileName = "deliveryCreationAgainstSo";

      const transformedManagers = await getCountryManagersOfDistributor(
        distributerId
      );

      const tranformedLogisticsPeople = await getDepartmentsPeoples(
        "LOGISTICS_AND_PAYMENTS"
      );

      const tranformedSupplyChainPeople = await getDepartmentsPeoples(
        "SUPPLY_CHAIN_OPERATIONS"
      );

      recipients = onlyUniqueRecipients([
        ...transformedManagers,
        ...tranformedLogisticsPeople,
        ...tranformedSupplyChainPeople,
      ]);
    } else if (nextStatus._id.toString() === "663c4b5f864c679bd21974b4") {
      /* Payment pending -------------------------------------------------------------------------------------------->>>>> */

      subject = `Titan Order Payment Pending ${company.name}_${piNumber}`;
      pugFileName = "paymentPending";

      const transformedManagers = await getCountryManagersOfDistributor(
        distributerId
      );
      const tranformedLogisticsPeople = await getDepartmentsPeoples(
        "LOGISTICS_AND_PAYMENTS"
      );

      recipients = onlyUniqueRecipients([
        ...transformedManagers,
        ...tranformedLogisticsPeople,
      ]);
    } else if (nextStatus._id.toString() === "663c4a84864c679bd21974a5") {
      /* PI generated -------------------------------------------------------------------------------------------->>>>> */

      subject = `Titan Order PI Generated ${company.name}_${piNumber}`;
      pugFileName = "piGenerated";
      const excelSheetKey = extractKeyFromURL(piDetails?.excelSheetUrl);
      const pdfSheetKey = extractKeyFromURL(piDetails?.pdfSheetUrl);

      const signedExcelSheetUrl = excelSheetKey
        ? await generateSignedURL(process.env.AWS_S3_BUCKET, excelSheetKey, 86400)
        : undefined;

      const signedPdfSheetUrl = pdfSheetKey
        ? await generateSignedURL(process.env.AWS_S3_BUCKET, pdfSheetKey, 86400)
        : undefined;

      emailAttachments = [signedExcelSheetUrl, signedPdfSheetUrl];

      const transformedManagers = await getCountryManagersOfDistributor(
        distributerId
      );

      const tranformedSupplyChainPeople = await getDepartmentsPeoples(
        "SUPPLY_CHAIN_OPERATIONS"
      );

      recipients = onlyUniqueRecipients([
        ...transformedManagers,
        ...tranformedSupplyChainPeople,
        {
          name: distributor.name,
          email: distributor.email,
        },
      ]);
    }

    if (recipients?.length > 0 && existingPiSheet && pugFileName) {
      await EmailNotification.create({
        emailCategory: "SHIPMENT",
        emailType: "SHIPMENT_STATUS_CHANGE",
        targetPugFile: pugFileName, // dynamic render the pug file
        reciepient: recipients,
        cc: ccRecipients?.map((x) => x.email) || [],
        emailPayload: {
          pseudoId: nextStatus.pseudoId,
          shipmentId: shipment_id,
          distributorName: distributor.name,
          piId: piDetails?.piId,
          piValue: piDetails?.piValue,
          piQuantity: piDetails?.totalQuantity,
          piDate: piDetails?.piSheetCreatedAtDate,
          subject: subject,
          companyName: company?.name,
          piNumber: name.split("-").join("/"),
          attachments: emailAttachments,
        },
      });
    }

    res.send({
      updateStatus,
    });
  } catch (error) {
    console.log("changeStatusAction", error);
    res.send({ error: "Something went wrong" });
  }
};

export async function getDepartmentsPeoples(pseudoId) {
  const department = await DepartmentType.findOne({
    pseudoId: pseudoId,
  }).select("_id");

  const departmentPeoples = await Department.find({
    departmentType: { $in: [department._id] },
  }).lean();

  const tranformedPeoples = departmentPeoples.map((people) => ({
    name: people.name,
    email: people.email,
  }));
  return tranformedPeoples;
}

export async function getCountryManagersOfDistributor(distributerId) {
  const countryManagerDepartment = await DepartmentType.findOne({
    pseudoId: "COUNTRY_MANAGER",
  }).select("_id");

  const distributorCountryManager = await Department.find({
    departmentType: { $in: [countryManagerDepartment._id] },
    distributor: { $in: [distributerId] },
  }).lean();

  const transformedManagers = distributorCountryManager.map((people) => ({
    name: people.name,
    email: people.email,
  }));

  return transformedManagers;
}

export function onlyUniqueRecipients(recipients) {
  const emails = [];

  const uniqueRecipients = recipients.filter((recipient) => {
    if (!emails.includes(recipient.email)) {
      emails.push(recipient.email);
      return true;
    } else {
      return false;
    }
  });

  return uniqueRecipients;
}

function validateFormData(schema, data) {
  return true; //this needs to be changed
  const test = schema.map((input) => {
    const submittedValue = data?.find(
      (x) => x.input_id.toString() === input._id.toString()
    )?.value;
    if (!submittedValue && input.is_mandatory) {
      return false;
    }
    if (submittedValue) {
      const dataType = typeof submittedValue;
      if (dataType !== input.value_type) {
        return false;
      }
    }
    return true;
  });

  if (test.includes(false)) {
    return false;
  } else {
    return true;
  }
}

export const triggerBatchProcessOrders = async (req, res) => {
  try {
    const currentUser = req.user;

    const hasSupplyChainOperations = currentUser?.departmentType?.some(
      (dept) => dept.pseudoId === "SUPPLY_CHAIN_OPERATIONS"
    );
    const hasAdmin = currentUser?.type === "admin_user";

    if (hasSupplyChainOperations || hasAdmin) {
      const ids = req.body;
      const abc = await processOrders(ids, res);
      // res.send(compareItems());
      // res.send(abc);
    } else {
      res.send({ message: "You are not authorized" });
    }
  } catch (error) {
    console.log("batchProcessOrdersError", error);

    await BulkProcessingStatus.findOneAndUpdate(
      { type: "Order Process" },
      {
        $setOnInsert: {
          type: "Order Process",
        },
        $set: {
          status: "FAILED",
          error: true,
          errorMessage: error,
        },
      },
      {
        upsert: true, // 👈 to create if not exists
        new: true,
      }
    );
  }
};

async function processOrders(ids, res) {
  try {
    const aggregationPipeline = [];

    await BulkProcessingStatus.findOneAndUpdate(
      { type: "Order Process" },
      {
        $setOnInsert: {
          type: "Order Process",
        },
        $set: {
          errorMessage: "",
          error: false,
          completion: 0,
          status: "RUNNING",
        },
      },
      {
        upsert: true,
        new: true,
      }
    );
    if (ids && ids.length) {
      aggregationPipeline.push({
        $match: {
          status: new mongoose.Types.ObjectId("6637fd72622d0e63dc195b94"), //status id for CREATED
          name: { $in: ids },
        },
      });
    } else {
      aggregationPipeline.push({
        $match: {
          status: new mongoose.Types.ObjectId("6637fd72622d0e63dc195b94"), //status id for CREATED
        },
      });
    }

    aggregationPipeline.push(
      {
        $lookup: {
          from: "distributors",
          localField: "distributor",
          foreignField: "_id",
          as: "distributor",
        },
      },
      {
        $unwind: {
          path: "$distributor",
        },
      },
      {
        $project: {
          _id: 1,
          line_items: 1,
          name: 1,
          shipping_address: 1,
          distributor: {
            _id: 1,
            name: 1,
            firstName: 1,
            lastName: 1,
            shopifyCompanyId: 1,
            shopifyCustomerId: 1,
            email: 1,
            phone: 1,
            priority: 1,
            country: 1,
          },
          shopifyCompanyId: 1,
        },
      }
    );

    const createdOrders = await Order.aggregate(aggregationPipeline);

    if (!createdOrders.length) {
      await BulkProcessingStatus.findOneAndUpdate(
        { type: "Order Process" },
        {
          $setOnInsert: {
            type: "Order Process",
          },
          $set: {
            errorMessage: "",
            error: false,
            completion: 100,
            status: "COMPLETED",
          },
        },
        {
          upsert: true,
          new: true,
        }
      );
      return res.status(204).json({ message: "Nothing to process" });
    }

    res.status(200).json({
      message: "Order processing initiated.",
    });

    //for dynamic status alignment, define initial status in status for both aligned and non-aligned shipments, then use the fetched ids here

    const alignedOrders = createdOrders.sort((a, b) => {
      const priorityA = a.distributor ? a.distributor.priority : Infinity;
      const priorityB = b.distributor ? b.distributor.priority : Infinity;
      return priorityA - priorityB;
    });

    const alignedShipmentStatus = await Status.findOne({
      _id: "663c4b27864c679bd21974ac",
    });
    const nonAlignedShipmentStatus = await Status.findOne({
      _id: "663c4ac9864c679bd21974a8",
    });

    const alignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [alignedShipmentStatus.departmentType] },
    });
    const nonAlignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [nonAlignedShipmentStatus.departmentType] },
    });

    const processor = fork(
      path.join(__dirname, "..", "service", "order", "orderProcessor.js")
    );

    processor.send({
      alignedOrders,
      alignedShipmentStatus,
      nonAlignedDepartmentPeoples,
      alignedDepartmentPeoples,
    });

    // Handle messages from child
    processor.on("message", (msg) => {
      console.log("Child message:", msg);

      processor.kill();
    });

    // Handle error
    processor.on("error", async (err) => {
      console.error("Child error:", err);
      await BulkProcessingStatus.findOneAndUpdate(
        { type: "Order Process" },
        {
          $set: {
            status: "FAILED",
            error: true,
            errorMessage: err,
          },
        },
        {
          new: true,
        }
      );
      process.kill();
    });

    return {
      allsorted: true,
    };
  } catch (error) {
    console.log("processOrderError", error);

    await BulkProcessingStatus.findOneAndUpdate(
      { type: "Order Process" },
      {
        $set: {
          error: true,
          errorMessage: error.message,
          status: "FAILED",
        },
      },
      { new: true }
    );

    return {
      error: error,
    };
  }
}

export const createShipments = async (order) => {
  const orderLineSkus = order.line_items.map((x) => x.sku);
  const existingInventory = await Inventory.aggregate([
    {
      $match: {
        sku: {
          $in: orderLineSkus,
        },
        // quantity: { $gt: 0 },
      },
    },
  ]);
  const data = compareItems(order.line_items, existingInventory);
  return data;
};

export const endEscalationStatus = async (shipment) => {
  const testUpdate = await Shipment.findOneAndUpdate(
    {
      _id: shipment._id,
      "escalation_history.status": shipment.status._id,
    },
    {
      $set: {
        "escalation_history.$.end": new Date(),
        escalation_status: false,
      },
    },
    { new: true }
  );
};

function compareItems(requestedItems, inventoryDocs) {
  const inventoryMap = new Map();

  // Map inventory by SKU
  inventoryDocs.forEach((doc) => {
    inventoryMap.set(doc.sku, doc);
  });

  const fulfilledItems = [];
  const remainingItems = [];
  const inventoryToUpdate = [];

  requestedItems.forEach((reqItem) => {
    const existingItem = inventoryMap.get(reqItem.sku);
    let totalFulfilled = 0;
    const sapSkusUsed = [];

    if (
      existingItem &&
      existingItem.sapSkus &&
      existingItem.sapSkus.length > 0
    ) {
      // Calculate total quantity in sapSkus
      const totalSapQuantity = existingItem.sapSkus.reduce(
        (acc, sapSku) => acc + sapSku.quantity,
        0
      );

      // If no stock is available in sapSkus, treat the item as unfulfilled
      if (totalSapQuantity === 0) {
        remainingItems.push({
          ...reqItem,
          requested: reqItem.quantity,
          fulfilled: 0,
          remaining: reqItem.quantity,
        });
        return; // Skip further processing for this item
      }

      let remainingQuantity = reqItem.quantity; // Start with the requested quantity

      for (const sapSku of existingItem.sapSkus) {
        if (remainingQuantity <= 0) break; // Stop if request is fully fulfilled

        const takeQuantity = Math.min(sapSku.quantity, remainingQuantity); // Take the smaller of what's available or needed
        if (takeQuantity > 0) {
          sapSkusUsed.push({
            sku: sapSku.sku,
            quantityTaken: takeQuantity,
            eanNo: sapSku.eanNo,
          });
          totalFulfilled += takeQuantity;
          remainingQuantity -= takeQuantity;
          sapSku.quantity -= takeQuantity; // Deduct from inventory

          // Prepare inventory update
          inventoryToUpdate.push({
            _id: sapSku._id,
            sku: sapSku.sku,
            quantity: sapSku.quantity, // Remaining quantity
            on_hold: takeQuantity, // Quantity held for this request
          });
        }
      }

      if (remainingQuantity === 0) {
        // Fully fulfilled
        fulfilledItems.push({
          ...reqItem,
          requested: totalFulfilled,
          fulfilled: totalFulfilled,
          remaining: 0,
          sapSkusUsed,
        });
      } else {
        // Partially fulfilled
        fulfilledItems.push({
          ...reqItem,
          requested: totalFulfilled,
          fulfilled: totalFulfilled,
          remaining: remainingQuantity,
          sapSkusUsed,
        });
        remainingItems.push({
          ...reqItem,
          requested: remainingQuantity,
          fulfilled: 0,
          remaining: remainingQuantity,
        });
      }
    } else {
      // Unfulfilled case: No sapSkus or insufficient quantity
      remainingItems.push({
        ...reqItem,
        requested: reqItem.quantity,
        fulfilled: 0,
        remaining: reqItem.quantity,
      });
    }
  });

  return {
    alignedShipment: fulfilledItems,
    nonAlignedShipment: remainingItems,
    inventoryToUpdate,
  };
}

// consume on hold inventory
async function consumeOnHoldInventory(inventoryToConsume) {
  console.log(inventoryToConsume);
  const bulkUpdateOps = inventoryToConsume.flatMap((x) =>
    x.sapSkusUsed.map((sapSku) => ({
      updateOne: {
        filter: {
          "sapSkus.sku": sapSku.sku,
          "sapSkus.eanNo": sapSku.eanNo,
        },
        update: {
          $inc: {
            "sapSkus.$[element].onHold": -sapSku.quantityTaken,
          },
        },
        arrayFilters: [
          {
            "element.sku": sapSku.sku,
          },
        ],
        upsert: false,
      },
    }))
  );

  try {
    const bulkWriteResult = await Inventory.bulkWrite(bulkUpdateOps);
  } catch (error) {
    console.error("Error during bulkWrite:", error);
  }
}

async function holdManualAllocatedInventory(inventoryToHold) {
  const skuMapLineItems = {};
  const allSkus = inventoryToHold.map((x) => {
    skuMapLineItems[x.sku] = x.requested;
    return x.sku;
  });
  const existingInventory = await Inventory.find({ sku: { $in: allSkus } });
  const notFulfillableSkus = [];
  for (let i = 0; i < existingInventory.length; i++) {
    const inventory = existingInventory[i];
    const availableInventory = inventory.quantity || 0;
    const requestedInventory = skuMapLineItems[inventory.sku];
    if (requestedInventory > availableInventory) {
      notFulfillableSkus.push(inventory.sku);
    }
  }

  if (notFulfillableSkus.length) {
    return {
      skus: notFulfillableSkus,
      message: "Sufficient quantity is not available.",
    };
  } else {
    const bulkUpdateOps = inventoryToHold.map((x) => ({
      updateOne: {
        filter: { sku: x.sku },
        update: {
          $inc: { quantity: x.requested * -1, on_hold: x.requested },
        },
      },
    }));
    const bulkwrite = await Inventory.bulkWrite(bulkUpdateOps);
  }
}

export async function alignmentPendingOrderProcess(req, res) {
  try {
    const ids = req.body;
    const result = await alignmentPendingOrderProcessTrigger(ids);
    let message =
      result.createdShipments.length > 0
        ? `${result.createdShipments.length} shipment created `
        : "";
    if (
      result.updatedShipments.length === 0 &&
      result.createdShipments.length === 0
    ) {
      message = "No inventory available";
    }
    res.status(200).json({
      message: message,
      data: result,
    });
  } catch (error) {
    console.log("processOrderError", error);
    return res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
}

export async function alignmentPendingOrderProcessTrigger(
  ids,
  statusActionLinkData
) {
  try {
    let alignmentPendingShipments = [];

    if (ids && ids.length) {
      alignmentPendingShipments = await Shipment.aggregate([
        {
          $match: {
            name: { $in: [...ids] },
          },
        },
        {
          $lookup: {
            from: "shipments",
            localField: "name",
            foreignField: "name",
            as: "shipment",
          },
        },
        {
          $unwind: {
            path: "$shipment",
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "order",
            foreignField: "_id",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    } else {
      alignmentPendingShipments = await Status.aggregate([
        {
          $match: {
            pseudoId: "ALIGNMENT_PENDING",
          },
        },
        {
          $lookup: {
            from: "shipments",
            localField: "_id",
            foreignField: "status",
            as: "shipment",
          },
        },
        {
          $unwind: {
            path: "$shipment",
          },
        },
        {
          $project: {
            shipment: 1,
            _id: 0,
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "shipment.order",
            foreignField: "_id",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    }

    if (!alignmentPendingShipments.length) {
      return "Nothing to process";
    }

    const createdShipments = [];
    const updatedShipments = [];

    const alignShipments = alignmentPendingShipments.sort(
      (a, b) => a.distributor.priority - b.distributor.priority
    );

    const alignedShipmentStatus = await Status.findOne({
      _id: "663c4b27864c679bd21974ac",
    });

    const nonAlignedShipmentStatus = await Status.findOne({
      _id: "663c4ac9864c679bd21974a8",
    });

    const alignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [alignedShipmentStatus.departmentType] },
    });

    const nonAlignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [nonAlignedShipmentStatus.departmentType] },
    });

    for (let i = 0; i < alignShipments.length; i++) {
      const order = alignShipments[i].order;
      const shipment = alignShipments[i].shipment;
      const distributor = alignShipments[i].distributor;
      const shipments = await createShipments({
        ...order,
        line_items: shipment.lineItems.map((item) => ({
          ...item,
          quantity: item.requested,
        })),
      });

      if (shipments.inventoryToUpdate.length) {
        const bulkUpdateOps = shipments.inventoryToUpdate.map((x) => ({
          updateOne: {
            filter: { "sapSkus._id": x._id, "sapSkus.sku": x.sku }, // Match the sapSkus array element by SKU
            update: {
              $set: {
                "sapSkus.$[element].quantity": x.quantity, // Update the quantity of the matching sapSku
              },
              $inc: {
                "sapSkus.$[element].onHold": x.on_hold, // Increment the on_hold value of the matching sapSku
              },
            },
            arrayFilters: [
              { "element.sku": x.sku }, // Ensure the update targets the correct sapSku
            ],
          },
        }));
        await Inventory.bulkWrite(bulkUpdateOps);
      }

      const allShipmentNames = [];

      if (shipments.alignedShipment.length) {
        const alignedUpdate = {
          status: alignedShipmentStatus._id, // Update the shipment to aligned status
          status_data: {
            name: alignedShipmentStatus.status,
          },
          lineItems: shipments.alignedShipment,
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment updated to aligned.`,
            },
            status_change_history: {
              status: alignedShipmentStatus._id,
            },
          },
        };

        const updatedAlignedShipment = await Shipment.findByIdAndUpdate(
          shipment._id,
          alignedUpdate
        );

        allShipmentNames.push(updatedAlignedShipment.name);
        updatedShipments.push(updatedAlignedShipment.name);

        /* 
       Reimplemantation: Must not trigger the email in the shipment create action
      */

        // const alignedDepartmentNotified = await getDepartmentForCreated(
        //   alignedShipmentStatus._id
        // );
        // const company = await Company.findOne({
        //   shopifyCompanyId: distributor.shopifyCompanyId,
        // });
        // const recipients = alignedDepartmentPeoples.map((departmentPeople) => ({
        //   name: departmentPeople.name,
        //   email: departmentPeople.email,
        //   cc: Array.isArray(alignedDepartmentNotified)
        //     ? [...alignedDepartmentNotified]
        //     : [],
        // }));

        // if (recipients.length > 0) {
        //   await EmailNotification.create({
        //     emailCategory: "SHIPMENT",
        //     emailType: "SHIPMENT_CREATE",
        //     reciepient: recipients,
        //     emailPayload: {
        //       orderName: order.name,
        //       distributorName: distributor.name,
        //       date: order.createdAt,
        //       currentStatus: "",
        //       nextStatus: alignedShipmentStatus.status,
        //       shipmentName: updatedAlignedShipment.name,
        //       shipmentStatus: alignedShipmentStatus.status,
        //       shipmetRef: updatedAlignedShipment.name,
        //       piName: updatedAlignedShipment?.piName,
        //       company: company.name,
        //     },
        //   });
        // }
      }

      if (!shipments.alignedShipment.length) {
        continue;
      }
      if (shipments.nonAlignedShipment.length) {
        // Calculate the total amount for the non-aligned shipment
        const totalNonAlignedAmount = shipments.nonAlignedShipment.reduce(
          (acc, item) => acc + item.requested * item.price,
          0
        );

        // Create base data for the non-aligned shipment
        let nonAligned = {
          name: `${order.name}_${
            (parseInt(shipment.name.split("_")[1], 10) || 0) + 1
          }`,
          order: order._id,
          order_data: {
            _id: order._id,
            name: order.name,
          },
          distributor_data: {
            name: distributor.name,
            _id: distributor._id,
            email: distributor.email,
            country: distributor.country,
          },
          status_data: {
            name: alignedShipmentStatus.status,
          },
          status: nonAlignedShipmentStatus._id, // Use dynamic status ID
          amount: totalNonAlignedAmount, // Sum of all non-aligned items
          shipping_address: order.shipping_address,
          lineItems: shipments.nonAlignedShipment,
          timeline: [
            {
              time: new Date(),
              comment: `Non-Aligned shipment created.`,
            },
          ],
          status_change_history: [
            {
              status: nonAlignedShipmentStatus._id, // Use dynamic status ID
            },
          ],
          initial_status: "non-aligned",
        };

        // Check for existing shipment with the same name and resolve conflicts
        let existName = await Shipment.findOne({ name: nonAligned.name });
        while (existName) {
          nonAligned.name =
            nonAligned.name.split("_")[0] +
            "_" +
            (parseInt(nonAligned.name.split("_")[1], 10) + 1);
          existName = await Shipment.findOne({ name: nonAligned.name });
        }

        // Create the shipment
        const createdNonAlignedShipment = await new Shipment(nonAligned).save();

        /* 
       Reimplemantation: Must not trigger the email in the shipment create action
      */

        // Notify departments
        // const nonAlignedDepartmentNotified = await getDepartmentForCreated(
        //   nonAlignedShipmentStatus._id
        // );
        // const company = await Company.findOne({
        //   shopifyCompanyId: distributor.shopifyCompanyId,
        // });

        // const recipients = nonAlignedDepartmentPeoples.map(
        //   (departmentPeople) => ({
        //     name: departmentPeople.name,
        //     email: departmentPeople.email,
        //     cc: Array.isArray(nonAlignedDepartmentNotified)
        //       ? [...nonAlignedDepartmentNotified]
        //       : [],
        //   })
        // );

        // if (recipients.length > 0) {
        //   await EmailNotification.create({
        //     emailCategory: "SHIPMENT",
        //     emailType: "SHIPMENT_CREATE",
        //     reciepient: recipients,
        //     emailPayload: {
        //       orderName: order.name,
        //       distributorName: distributor.name,
        //       date: order.createdAt,
        //       currentStatus: "",
        //       nextStatus: nonAlignedShipmentStatus.status,
        //       shipmentName: createdNonAlignedShipment.name,
        //       shipmentStatus: nonAlignedShipmentStatus.status,
        //       shipmetRef: createdNonAlignedShipment.name,
        //       piName: createdNonAlignedShipment?.piName,
        //       company: company.name,
        //     },
        //   });
        // }
        createdShipments.push(createdNonAlignedShipment.name);
        allShipmentNames.push(createdNonAlignedShipment.name);
      }

      const valuesToPush = allShipmentNames.map((x) => ({
        time: new Date(),
        comment: `Shipment ${x} updated.`,
      }));

      await Order.findByIdAndUpdate(order._id, {
        $set: {
          status: "664372f129f6db844ee8bc0b", // this needs to be dynamic
        },
        $push: { timeline: { $each: valuesToPush } },
      });
    }

    return { createdShipments, updatedShipments };
  } catch (error) {
    console.log("processOrderError", error);
  }
}

//  for (let i = 0; i < alignedOrders.length; i++) {
//    const order = alignedOrders[i].order;
//    const distributor = alignedOrders[i].distributor;
//    const shipments = await createShipments(order);
//    if (shipments.inventoryToUpdate.length) {
//      const bulkUpdateOps = shipments.inventoryToUpdate.map((x) => ({
//        updateOne: {
//          filter: { "sapSkus._id": x._id, "sapSkus.sku": x.sku }, // Match the sapSkus array element by SKU
//          update: {
//            $set: {
//              "sapSkus.$[element].quantity": x.quantity, // Update the quantity of the matching sapSku
//            },
//            $inc: {
//              "sapSkus.$[element].onHold": x.on_hold, // Increment the on_hold value of the matching sapSku
//            },
//          },
//          arrayFilters: [
//            { "element.sku": x.sku }, // Ensure the update targets the correct sapSku
//          ],
//        },
//      }));

//      const bulkWriteResult = await Inventory.bulkWrite(bulkUpdateOps);
//    }
//    const allShipmentNames = [];
//    let shipment_number = 1;
//    if (shipments.alignedShipment.length) {
//      const aligned = {
//        name: order.name + `_${shipment_number}`,
//        order: order._id,
//        order_data: {
//          _id: order._id,
//          name: order.name,
//        },
//        distributor_data: {
//          name: distributor.name,
//          _id: distributor._id,
//          email: distributor.email,
//          country: distributor.country,
//        },
//        sapSkusUsed: [shipments.alignedShipment.sapSkusUsed],
//        status: "663c4b27864c679bd21974ac", //TODO this need to be dynamic
//        status_data: {
//          name: alignedShipmentStatus.status,
//        },
//        amount: shipments.alignedShipment.reduce((acc, lineItem) => {
//          return acc + lineItem.requested * lineItem.price;
//        }, 0),
//        shipping_address: order.shipping_address,
//        lineItems: shipments.alignedShipment,
//        timeline: [
//          {
//            time: new Date(),
//            comment: `Aligned shipment created.`,
//          },
//        ],
//        status_change_history: [
//          {
//            status: "663c4b27864c679bd21974ac", //TODO this need to be dynamic
//          },
//        ],
//        initial_status: "aligned",
//      };
//      const createdAlignedShipment = await new Shipment(aligned).save();
//      allShipmentNames.push(aligned.name);
//      shipment_number++;

//      const alignedDepartmentNotified = await getDepartmentForCreated(
//        alignedShipmentStatus._id
//      );
//      const company = await Company.findOne({
//        shopifyCompanyId: distributor.shopifyCompanyId,
//      });
//      const recipients = alignedDepartmentPeoples.map((departmentPeople) => ({
//        name: departmentPeople.name,
//        email: departmentPeople.email,
//        cc: Array.isArray(alignedDepartmentNotified)
//          ? [...alignedDepartmentNotified]
//          : [],
//      }));

//      /*
//        Reimplemantation: Must not trigger the email in the shipment create action
//       */

//      // if (recipients.length > 0) {
//      //   await EmailNotification.create({
//      //     emailCategory: "SHIPMENT",
//      //     emailType: "SHIPMENT_CREATE",
//      //     reciepient: recipients,
//      //     emailPayload: {
//      //       orderName: order.name,
//      //       distributorName: distributor.name,
//      //       date: order.createdAt,
//      //       currentStatus: "",
//      //       nextStatus: alignedShipmentStatus.status,
//      //       shipmentName: createdAlignedShipment.name,
//      //       shipmentStatus: alignedShipmentStatus.status,
//      //       shipmetRef: aligned.name,
//      //       piName: createdAlignedShipment?.piName,
//      //       company: company.name,
//      //     },
//      //   });
//      // }
//    }
//    if (shipments.nonAlignedShipment.length) {
//      const nonAligned = {
//        name: order.name + `_${shipment_number}`,
//        order: order._id,
//        order_data: {
//          _id: order._id,
//          name: order.name,
//        },
//        distributor_data: {
//          name: distributor.name,
//          _id: distributor._id,
//          email: distributor.email,
//          country: distributor.country,
//        },
//        status_data: {
//          name: alignedShipmentStatus.status,
//        },
//        status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
//        amount: shipments.nonAlignedShipment.reduce((acc, lineItem) => {
//          return acc + lineItem.requested * lineItem.price;
//        }, 0),
//        shipping_address: order.shipping_address,
//        lineItems: shipments.nonAlignedShipment,
//        timeline: [
//          {
//            time: new Date(),
//            comment: `Non-Aligned shipment created.`,
//          },
//        ],
//        status_change_history: [
//          {
//            status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
//          },
//        ],
//        initial_status: "non-aligned",
//      };
//      const createdNonAlignedShipment = await new Shipment(nonAligned).save();

//      const nonAlignedDepartmentNotified = await getDepartmentForCreated(
//        alignedShipmentStatus._id
//      );

//      const company = await Company.findOne({
//        shopifyCompanyId: distributor.shopifyCompanyId,
//      });
//      const recipients = nonAlignedDepartmentPeoples.map((departmentPeople) => ({
//        name: departmentPeople.name,
//        email: departmentPeople.email,
//        cc: Array.isArray(nonAlignedDepartmentNotified)
//          ? [...nonAlignedDepartmentNotified]
//          : [],
//      }));

//      /*
//        Reimplemantation: Must not trigger the email in the shipment create action
//       */

//      // if (recipients.length > 0) {
//      //   await EmailNotification.create({
//      //     emailCategory: "SHIPMENT",
//      //     emailType: "SHIPMENT_CREATE",
//      //     reciepient: recipients,
//      //     emailPayload: {
//      //       orderName: order.name,
//      //       distributorName: distributor.name,
//      //       date: order.createdAt,
//      //       currentStatus: "",
//      //       nextStatus: nonAlignedShipmentStatus.status,
//      //       shipmentName: createdNonAlignedShipment.name,
//      //       shipmentStatus: nonAlignedShipmentStatus.status,
//      //       shipmetRef: nonAligned.name,
//      //       piName: nonAlignedShipmentStatus?.piName,
//      //       company: company.name,
//      //     },
//      //   });
//      //   allShipmentNames.push(nonAligned.name);
//      // }
//    }
//    const valuesToPush = allShipmentNames.map((x) => ({
//      time: new Date(),
//      comment: `Shipment ${x} created.`,
//    }));

//    const updateorder = await Order.findByIdAndUpdate(order._id, {
//      $set: {
//        status: "664372f129f6db844ee8bc0b", //this need to be dynamic
//      },
//      $push: { timeline: { $each: valuesToPush } },
//    });

//    console.log(" I am running inside this loop");

//    const isCompleted =
//      ((i + 1) / alignedOrders.length) * 100 === 100 ? "Completed" : "Running";

//    await BulkProcessingStatus.findOneAndUpdate(
//      { type: "Order Process" },
//      {
//        $set: {
//          completion: ((i + 1) / alignedOrders.length) * 100,
//          status: isCompleted,
//        },
//      },
//      { new: true }
//    );
//  }
