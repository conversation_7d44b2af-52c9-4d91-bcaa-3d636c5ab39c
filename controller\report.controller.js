import Order from "../model/order.model.js";
import Distributor from "../model/distributor.model.js";
import Department from "../model/department.model.js";

// Helper function to calculate percentage change
function calculatePercentageChange(current, previous) {
  if (previous === 0) {
    return current * 100; // Consider full increase
  }
  return ((current - previous) / previous) * 100;
}

export const calculateStats = async (req, res) => {
  const now = new Date();
  const last30DaysEnd = new Date(now); // Today
  const last30DaysStart = new Date(now.setDate(now.getDate() - 30));

  const previous30DaysEnd = new Date(last30DaysStart);
  const previous30DaysStart = new Date(previous30DaysEnd);
  previous30DaysStart.setDate(previous30DaysStart.getDate() - 30);

  try {
    const [
      ordersLast30Days,
      distributorsLast30Days,
      countryManagersResult,
      ordersPrev30Days,
      distributorsPrev30Days,
      countryManagersPrevResult,
    ] = await Promise.all([
      Order.countDocuments({
        createdAt: { $gte: last30DaysStart, $lt: last30DaysEnd },
      }),
      Distributor.countDocuments({
        createdAt: { $gte: last30DaysStart, $lt: last30DaysEnd },
      }),
      Department.aggregate([
        {
          $match: { createdAt: { $gte: last30DaysStart, $lt: last30DaysEnd } },
        },
        {
          $lookup: {
            from: "designations",
            localField: "designation",
            foreignField: "_id",
            as: "designationDetails",
          },
        },
        { $unwind: "$designationDetails" },
        { $match: { "designationDetails.isCountryManager": true } },
        { $count: "countryManagersCount" },
      ]),
      Order.countDocuments({
        createdAt: { $gte: previous30DaysStart, $lt: previous30DaysEnd },
      }),
      Distributor.countDocuments({
        createdAt: { $gte: previous30DaysStart, $lt: previous30DaysEnd },
      }),
      Department.aggregate([
        {
          $match: {
            createdAt: { $gte: previous30DaysStart, $lt: previous30DaysEnd },
          },
        },
        {
          $lookup: {
            from: "designations",
            localField: "designation",
            foreignField: "_id",
            as: "designationDetails",
          },
        },
        { $unwind: "$designationDetails" },
        { $match: { "designationDetails.isCountryManager": true } },
        { $count: "countryManagersCount" },
      ]),
    ]);

    const countryManagersLast30Days =
      countryManagersResult.length > 0
        ? countryManagersResult[0].countryManagersCount
        : 0;
    const countryManagersPrev30Days =
      countryManagersPrevResult.length > 0
        ? countryManagersPrevResult[0].countryManagersCount
        : 0;

    // Calculate percentage changes
    const ordersPercentageChange = calculatePercentageChange(
      ordersLast30Days,
      ordersPrev30Days
    );
    const distributorsPercentageChange = calculatePercentageChange(
      distributorsLast30Days,
      distributorsPrev30Days
    );
    const countryManagersPercentageChange = calculatePercentageChange(
      countryManagersLast30Days,
      countryManagersPrev30Days
    );

    // Determine trends
    const ordersTrend = ordersPercentageChange > 0 ? "increased" : "decreased";
    const distributorsTrend =
      distributorsPercentageChange > 0 ? "increased" : "decreased";
    const countryManagersTrend =
      countryManagersPercentageChange > 0 ? "increased" : "decreased";

    // Return stats
    const statsArray = [
      {
        heading: "Orders",
        currentCount: ordersLast30Days,
        pastCount: ordersPrev30Days,
        changeType: ordersTrend,
        percentageChange: ordersPercentageChange,
      },
      {
        heading: "Distributors",
        currentCount: distributorsLast30Days,
        pastCount: distributorsPrev30Days,
        changeType: distributorsTrend,
        percentageChange: distributorsPercentageChange,
      },
      {
        heading: "Country Managers",
        currentCount: countryManagersLast30Days,
        pastCount: countryManagersPrev30Days,
        changeType: countryManagersTrend,
        percentageChange: countryManagersPercentageChange,
      },
    ];

    return res.status(200).json({ statsArray });
  } catch (error) {
    console.error("Error calculating stats:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
