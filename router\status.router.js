import express from "express";
import {
  getStatus,
  createStatus,
  getOneStatus,
  updateStatus,
  deleteStatus,
  deleteStatusCustomField,
  updateStatusCustomField,
  filterStatusPayloadKeys,
} from "../controller/status.controller.js";
import authenticateDepttPeopleAccess, {
  authenticateAdminPeopleAccess,
} from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateDepttPeopleAccess("status", "read"), getStatus)
  .post(authenticateAdminPeopleAccess("status", "write"), createStatus);

router
  .route("/:id/custom_field/:fieldId")
  .delete(
    authenticateAdminPeopleAccess("status", "delete"),
    deleteStatusCustomField
  );

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("status", "read"), getOneStatus)
  .patch(
    authenticateAdminPeopleAccess("status", "write"),
    filterStatusPayloadKeys,
    updateStatusCustomField,
    updateStatus
  )
  .delete(authenticateAdminPeopleAccess("status", "delete"), deleteStatus);

export default router;
