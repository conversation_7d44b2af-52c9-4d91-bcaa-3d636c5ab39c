import mongoose from "mongoose";
import axios from "axios";
import Distributor from "../model/distributor.model.js";
import catchAsync from "../utils/catchAsync.js";
import { deleteOne, getOne, updateOne } from "../utils/controllerFactory.js";
import AppError from "../utils/appError.js";

import Order from "../model/order.model.js";

import APIFeatures from "../utils/apiFeatures.js";

export const getOneDistributor = getOne(Distributor);
export const updateDistributor = updateOne(Distributor);
export const deleteDistributor = deleteOne(Distributor);

const ObjectId = mongoose.Types.ObjectId;

export const createDistributor = catchAsync(async (req, res, next) => {
  const token = req.body.token;
  if (!req.body.password) {
    return res.status(400).json({ message: "Password is required" });
  }
  const createdDistributor = await Distributor.create(req.body);
  createdDistributor.password = undefined;
  res.status(201).json({ createdDistributor, token });
});

export const removeEmailFromPayload = catchAsync(async (req, res, next) => {
  const myHeaders = new Headers();
  myHeaders.append(
    "X-Shopify-Access-token",
    `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`
  );

  const requestOptions = {
    method: "GET",
    headers: myHeaders,
    redirect: "follow",
  };

  const responsePromise = await fetch(
    `${process.env.SHOP_URL}/admin/api/2021-07/customers/search.json?query=${req.body.email}&fields=email`,
    requestOptions
  );
  const response = await responsePromise.json();

  if (response.customers.length > 0) {
    return next(
      new AppError("Cannot update email. As same email already exists.", 400)
    );
  }
  next();
});

export const updateDistributorPriority = catchAsync(async (req, res, next) => {
  const distributorData = req.body.distributors;
  const bulkOps = distributorData.map((update) => ({
    updateOne: {
      filter: { _id: update._id },
      update: { $set: { priority: update.priority } },
    },
  }));

  const result = await Distributor.bulkWrite(bulkOps);
  res.status(200).json({
    status: "success",
    // matchedCount: result.matchedCount,
    // modifiedCount: result.modifiedCount
    data: result,
  });
});

export const createCustomerInShopify = async (req, res, next) => {
  try {
    const customerDetails = req.body;
    if (
      !customerDetails.shopifyCompanyId ||
      !customerDetails.email ||
      !customerDetails.firstName ||
      !customerDetails.lastName ||
      !customerDetails.phone
    ) {
      return next(
        new AppError(
          "Missing any of the following required fields [shopifyCompanyId, email, firstName, lastName, phone]",
          400
        )
      );
    }
    if (customerDetails.shopifyCompanyId?.toString()?.includes("gid")) {
      return next(new AppError("Invalid CompanyId", 400));
    }
    const data = JSON.stringify({
      query: `mutation companyContactCreate($companyId: ID!, $input: CompanyContactInput!) {
        companyContactCreate(companyId: $companyId, input: $input) {
          companyContact {
            id
            customer {
              id
            }
          }
          userErrors {
            field
            message
          }
        }
      }`,
      variables: {
        companyId: `gid://shopify/Company/${customerDetails.shopifyCompanyId}`,
        input: {
          email: `${customerDetails.email}`,
          firstName: `${customerDetails.firstName}`,
          lastName: `${customerDetails.lastName}`,
          // "phone": `${customerDetails.phone}`
        },
      },
    });

    const config = {
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      data: data,
    };
    const customerCreateResponse = await axios(config);
    const customerId =
      customerCreateResponse.data?.data?.companyContactCreate?.companyContact?.customer?.id
        ?.split("/")
        ?.pop();

    if (!customerId) {
      return next(
        new AppError(
          `Error occured while creating customer in shopify [${customerCreateResponse?.data?.data?.companyContactCreate?.userErrors[0]?.message}]`,
          400
        )
      );
    }

    // const activationUrlConfig = {
    //   method: 'post',
    //   url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/account_activation_url.json`,
    //   headers: {
    //     'X-Shopify-Access-Token': `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
    //     'Content-Type': 'application/json'
    //   },
    //   data: JSON.stringify({})
    // };
    // console.log('herew22');

    // const accountActivationUrlResponse = await axios(activationUrlConfig);
    // const activationUrl = accountActivationUrlResponse.data?.account_activation_url;
    // console.log('accountActivationUrlResponse', accountActivationUrlResponse);
    // console.log('activationUrl', activationUrl);
    // if (activationUrl) {
    //   await EmailNotification.create(({
    //     emailCategory: 'CUSTOMER',
    //     emailType: 'CUSTOMER_ACCOUNT_ACTIVATION',
    //     reciepient: {
    //       name: customerDetails.firstName,
    //       email: customerDetails.email
    //     },
    //     emailPayload: {
    //       activationUrl: activationUrl
    //     },
    //   }));
    // }
    req.body.shopifyCustomerId = customerId;
    // return customerCreateResponse.data;
    next();
  } catch (error) {
    console.error(error);
    return next(new AppError(error, 400));
  }
};

export const createCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const distributorId = req.body.distributor;
  const first_name = req.body.first_name;
  const last_name = req.body.last_name;
  const company = req.body.company || "";
  const address1 = req.body.address1;
  const address2 = req.body.address2 || "";
  const city = req.body.city;
  const province = req.body.province;
  const country = req.body.country;
  const zip = req.body.zip;
  const province_code = req.body.province_code || "";
  const country_code = req.body.country_code || "";
  const isDefault = req.body.isDefault || false;
  const country_name = req.body.country_name || "";
  if (
    !customerId ||
    !first_name ||
    !last_name ||
    !address1 ||
    !distributorId ||
    !city ||
    !country ||
    !zip
  ) {
    return next(
      new AppError(
        "Missing any of the following field [distributor, shopifyCustomerId, first_name, last_name, address1, city, province, country, zip]",
        400
      )
    );
  }
  const data = JSON.stringify({
    customer_address: {
      first_name: first_name,
      last_name: last_name,
      company: company,
      address1: address1,
      address2: address2,
      city: city,
      // "province": province,
      country: country,
      zip: zip,
      // "phone": "************",
      // "province_code": province_code,
      country_code: country_code,
      country_name: country_name,
      default: isDefault,
    },
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/customers/${customerId}/addresses.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
    .then(function (response) {
      req.body.shopifyAddressId = response.data.customer_address.id;
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const updateCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const addressId = req.body.shopifyAddressId;
  if (!customerId || !addressId) {
    return next(
      new AppError(
        "Missing any of the following field [shopifyCustomerId, shopifyAddressId]",
        400
      )
    );
  }
  const payload = req.body;
  var data = JSON.stringify({
    customer_address: payload,
  });

  var config = {
    method: "put",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/addresses/${addressId}.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    data: data,
  };
  axios(config)
    .then(function (response) {
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const deleteCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const addressId = req.body.shopifyAddressId;
  if (!customerId || !addressId) {
    return next(
      new AppError(
        "Missing any of the following field [shopifyCustomerId, shopifyAddressId]",
        400
      )
    );
  }
  const config = {
    method: "delete",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/addresses/${addressId}.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
    },
  };
  axios(config)
    .then(function (response) {
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const checkIfDistributorHasOrderOrNot = catchAsync(
  async (req, res, next) => {
    const distributorObjectId = new ObjectId(req.params.id);
    const distributorOrderExist = await Order.find({
      distributor: distributorObjectId,
    });
    const distributorId = await Distributor.findOne({
      _id: distributorObjectId,
    });

    if (distributorOrderExist.length <= 0) {
      const myHeaders = new Headers();
      myHeaders.append(
        "X-Shopify-Access-Token",
        `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`
      );
      myHeaders.append("Content-Type", "application/json");

      const graphql = JSON.stringify({
        query: `mutation MyMutation {\r\n    customerDelete(input: {id: \"gid://shopify/Customer/${distributorId.shopifyCustomerId}\"}) {\r\n        deletedCustomerId\r\n    }\r\n}`,
        variables: {},
      });
      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: graphql,
        redirect: "follow",
      };

      const responsePromise = await fetch(
        `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
        requestOptions
      );
      const response = await responsePromise.json();
    }

    next();
  }
);

export const updateDistributorInShopify = catchAsync(async (req, res, next) => {
  const distributorData = req.body;

  const data = JSON.stringify({
    query: `mutation customerUpdate($input: CustomerInput!) {
      customerUpdate(input: $input) {
        userErrors {
          field
          message
        }
        customer {
          id
          firstName
          lastName
        }
      }
    }`,
    variables: {
      input: {
        id: `gid://shopify/Customer/${distributorData.id}`,
        firstName: distributorData.firstName,
        lastName: distributorData.lastName,
        phone: distributorData.phone,
        note: distributorData.note,
      },
    },
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/graphql.json`,
    headers: {
      "X-Shopify-Access-token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  const response = await axios(config);

  next();
});

export const getCountryManagerDistributors = catchAsync(async (req, res) => {
  let query;
  const isCountryManager =
    req.user?.designation?.isCountryManager &&
    Array.isArray(req.user.distributor);

  // If user is a country manager, filter orders by their assigned distributors
  if (isCountryManager) {
    const distributorIds = req.user.distributor.map((d) => d._id);
    query = Distributor.find(
      { _id: { $in: distributorIds } },
      {
        password: 0,
      }
    );
  } else {
    // If not a country manager, return all orders
    query = Distributor.find({}, { password: 0 });
  }

  const features = new APIFeatures(query, req.query)
    .filter()
    .sort()
    .limitFields()
    .paginate();

  const doc = await features.query;

  const totalCount = isCountryManager
    ? await Distributor.countDocuments({
        distributor: { $in: req.user.distributor.map((d) => d._id) },
      })
    : await Distributor.countDocuments();

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: doc,
    },
  });
});
