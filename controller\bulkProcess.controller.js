import BulkProcessingStatus from "../model/bulkProcessing.model.js";

export const getCurrentRunningStatus = async (req, res) => {
  try {
    const foundOrderProcessStatus = await BulkProcessingStatus.findOne({
      type: "Order Process",
    });

    if (!foundOrderProcessStatus) {
      return res.status(200).json({
        responseCode: 1,
        data: {
          isProcessRunning: false,
        },
      });
    }

    if (foundOrderProcessStatus.error) {
      return res.status(200).json({
        responseCode: 1,
        data: {
          isProcessRunning: false,
          error: foundOrderProcessStatus.error,
          errorMessage: foundOrderProcessStatus.errorMessage,
          progress: foundOrderProcessStatus.completion,
        },
      });
    }

    return res.status(200).json({
      responseCode: 1,
      data: {
        isProcessRunning: foundOrderProcessStatus.status === "RUNNING",
        error: false,
        progress: foundOrderProcessStatus.completion,
      },
    });
  } catch (err) {
    console.log("Error in getting the current status of the process", err);
    return res.status(500).json({
      responseCode: 0,
      data: {
        isProcessRunning: false,
        error: true,
        progress: foundOrderProcessStatus.completion,
      },
    });
  }
};

export const acknowledgeProcessing = async (req, res) => {
  try {
    await BulkProcessingStatus.findOneAndUpdate(
      { type: "Order Process" },
      {
        $set: {
          error: false,
          errorMessage: false,
          completion: 0,
          status: "IDLE",
        },
      }
    );

    res.status(200).json({
      responseCode: 1,
      message: "Successfully Acknowledged",
      data: {
        acknowledged: true,
      },
    });
  } catch (err) {
    console.log(" Error has been occured while acknowledging", err);
    res.status(500).json({
      responseCode: 0,
      errorMessage: "Error in acknowledging the order process.",
      data: {
        acknowledged: false,
      },
    });
  }
};
