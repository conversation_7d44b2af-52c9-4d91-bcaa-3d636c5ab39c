import axios from "axios";
import Status from "../model/status.model.js";
import AppError from "../utils/appError.js";
import multer from "multer";
import xlsx from "xlsx";
import {
  validateProductSheet,
  validateProductSheetHeader,
} from "../utils/helperFunction.js";
import Distributor from "../model/distributor.model.js";
import Cart from "../model/cart.model.js";

const upload = multer({ dest: "uploads/" });

export const createOrder = async (req, res, next) => {
  try {
    const initialOrderStatusExist = await Status.findOne({
      isInitialStatus: true,
      statusType: "Order",
    });
    const initialShipmentStatusExist = await Status.findOne({
      isInitialStatus: true,
      statusType: "Shipment",
    });
    if (!initialShipmentStatusExist || !initialOrderStatusExist)
      return next(
        new AppError(
          "No Initial Status Available in Admin Dashboard for Order Or Shipment"
        )
      );
    const is_line_items =
      req.body.order.line_items && req.body.order.line_items.length > 0;
    const is_transactions =
      req.body.order.transactions && req.body.order.transactions.length > 0;
    const is_total_tax = req.body.order.total_tax;
    const is_currency = req.body.order.currency;
    if (!is_line_items || !is_transactions || !is_total_tax || !is_currency)
      return next(
        new AppError(
          "One of the following required field is missing line_items[] or transactions[] or total_tax or currency"
        )
      );
    const data = JSON.stringify(req.body);
    const config = {
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/orders.json`,
      headers: {
        "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      data: data,
    };

    const productCreateResponse = await axios(config);
    res.status(200).json({
      status: "success",
      data: productCreateResponse.data,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "fail",
      error: error.response.data.errors,
      errorStack: JSON.stringify(error),
    });
  }
};

export const getCompanyLocations = async (companyId) => {
  const data = JSON.stringify({
    query: `{company(id: "${companyId}") {
    locations(first: 2) {
      nodes {
        billingAddress {
          address1
          address2
          city
          companyName
          country
          countryCode
          firstName
          phone
          province
          zip
          zoneCode
        }
         shippingAddress {
          address1
          address2
          city
          companyName
          country
          countryCode
          firstName
          phone
          province
          zip
          zoneCode
        }
        orderCount
        note
      }
    }
  }}`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  return axios(config)
    .then(function (response) {
      const companyLocations =
        response.data.data.company?.locations?.nodes || {};
      return companyLocations;
    })
    .catch(function (error) {
      console.log(error);
    });
};

export const getCustomerInfo = async (customerId) => {
  const data = JSON.stringify({
    query: `{
      customer(id: "${customerId}") {
        displayName
        email
        phone
        companyContactProfiles {
          isMainContact
          company {
            id
            name
          }
        }
      }
    }`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-07/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };
  return axios(config)
    .then(function (response) {
      const customer = response.data.data.customer;
      const companyContactProfiles =
        response.data.data.customer?.companyContactProfiles;
      const companyInfo = companyContactProfiles
        ? companyContactProfiles[0]?.company
        : {};
      return {
        customer: {
          shopifyCustomerId: customerId.split("/")?.pop(),
          name: customer?.displayName,
          email: customer?.email,
          phone: customer?.email,
          isMainContact: companyContactProfiles
            ? companyContactProfiles[0]?.isMainContact
            : "",
        },
        companyInfo: companyInfo,
      };
    })
    .catch(function (error) {
      console.log(error);
    });
};

export const getCompanyList = async (req, res, next) => {
  const data = JSON.stringify({
    query: `{
      companies(first:250) {
          nodes {
              id
              name
          }
      }
  }`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
    .then(function (response) {
      const trasformedData = response.data.data.companies?.nodes?.map(
        (company) => {
          return {
            id: company.id.split("/").pop(),
            name: company.name,
          };
        }
      );
      res.status(200).send({
        status: "success",
        data: trasformedData,
      });
    })
    .catch(function (error) {
      console.log(error);
    });
};