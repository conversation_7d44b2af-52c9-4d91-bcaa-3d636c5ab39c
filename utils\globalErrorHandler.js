import AppError from "./appError.js";

const handleCastErrorDb = (error) => {
  const message = `Invalid ${error.path}: ${error.value}`;
  return new AppError(message, 400);
};

const handleDuplicateValueDb = (error) => {
  const value = error.errmsg.match(/(["'])(?:(?=(\\?))\2.)*?\1/)[0];
  const message = `The value ${value} already Exist. Please use another value`;
  return new AppError(message, 400);
};

const handleValidationErrorDb = (error) => {
  const errors = Object.values(error.errors).map((el) => el.message);
  const message = `Invalid input Data. ${errors.join(". ")}`;
  return new AppError(message, 400);
};

const handleJwtError = (err) =>
  new AppError("Invalid Token Please Login Again ! ", 401);

const handleJWTExpiredError = (err) =>
  new AppError("JWT Expired Please Login Again !", 401);

const sendErrorDev = (error, res) => {
  res.status(error.statusCode).json({
    status: error.status,
    error: error,
    message: error.message,
    stack: error.stack,
  });
};

const sendErrorProd = (error, res) => {
  if (error.isOperational) {
    res.status(error.statusCode).json({
      status: error.status,
      message: error.message,
    });
  } else {
    console.error("Error 💥", error);
    res.status(500).json({
      status: "error",
      message: "Something went wrong!",
    });
  }
};

export default (error, req, res, next) => {
  error.statusCode = error.statusCode || 500;
  error.status = error.status || "error";

  if (process.env.NODE_ENV == "development") {
    sendErrorDev(error, res);
  } else if (process.env.NODE_ENV == "production") {
    // let err = { ...error }
    // err.message = error.message
    if (error.name == "CastError") error = handleCastErrorDb(error);
    if (error.code == 11000) error = handleDuplicateValueDb(error);
    if (error.name == "ValidationError") error = handleValidationErrorDb(error);
    if (error.name === "JsonWebTokenError") error = handleJwtError(error);
    if (error.name === "TokenExpiredError")
      error = handleJWTExpiredError(error);
    sendErrorProd(error, res);
  }
};
