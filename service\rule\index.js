import sgMail from "@sendgrid/mail";
import { htmlToText } from "html-to-text";
import path from "path";
import pug from "pug";
import { readFileSync } from "fs";
import axios from "axios";

import dotenv from "dotenv";
dotenv.config();

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

const month = [
  "JAN",
  "FEB",
  "MAR",
  "APR",
  "May",
  "JUN",
  "JUL",
  "AUG",
  "SEP",
  "OCT",
  "NOV",
  "DEC",
];
const d = new Date();
const currentMonth = month[d.getMonth()];
const currentYear = d.getFullYear();

sgMail.setApiKey(process.env.SENDGRID_PASSWORD);

// try {
//   await sgMail.send({
//     to: "<EMAIL>",
//     from: "<EMAIL>", // must be verified!
//     subject: "Test Email from Titan",
//     text: "Hello, this is a test email using SendGrid's API",
//   });
// } catch (error) {
//   console.error("SendGrid error:", error);

//   // Safely access nested error data
//   if (error.response && error.response.body && error.response.body.errors) {
//     console.error("Detailed SendGrid errors:");
//     for (const err of error.response.body.errors) {
//       console.error(`- ${err.message}`);
//     }
//   } else {
//     console.error("No detailed error message received from SendGrid.");
//   }
// }

export class Email {
  constructor(
    reciepient,
    emailPayload,
    ccRecipients,
    emailCategory,
    targetPugFile
  ) {
    this.to = reciepient.map((r) => r.email);
    this.cc = ccRecipients || [];
    this.category = emailCategory;
    this.emailPayload = emailPayload;
    this.targetPugFile = targetPugFile;
    this.from = {
      name: "Titan",
      email: process.env.EMAIL_FROM, // Must be a verified sender in SendGrid
    };
  }

  async send(template, subject) {
    const templatePath = path.join(
      __dirname,
      "..",
      "..",
      `views/email/${template}.pug`
    );
    const html = pug.renderFile(templatePath, {
      emailPayload: this.emailPayload,
      subject,
    });

    const msg = {
      to: this.to,
      from: this.from,
      subject,
      html,
      text: htmlToText(html),
    };

    if (this.cc.length) {
      msg.cc = this.cc;
    }

    if (this.emailPayload.attachments) {
      msg.attachments = await Promise.all(
        this.emailPayload.attachments.map(async (url) => {
          const response = await axios.get(url, {
            responseType: "arraybuffer",
          });
          return {
            content: Buffer.from(response.data).toString("base64"),
            filename: path.basename(url.split("?")[0]), // strip query params
            type: "application/octet-stream",
            disposition: "attachment",
          };
        })
      );
    }

    const response = await sgMail.send(msg);
    console.log("Email sent:", response[0].statusCode);
  }

  async sendShipmentStatusChangeEmail() {
    await this.send(this.targetPugFile, `${this.emailPayload.subject}`);
  }

  async sendOrderDispachedEmail() {
    await this.send(
      "dispatchedFromWareHouse",
      `Titan Order Dispatched ${this.emailPayload.companyName}_${this.emailPayload.invoiceId}`
    );
  }

  async sendOrderCreationEmail() {
    await this.send(
      "orderCreated",
      `Titan Order Placed_${
        this.emailPayload?.company || ""
      }_${currentMonth}-${currentYear}_InternalTeam_${
        this.emailPayload?.orderID ?? ""
      }`
    );
  }

  async sendShipmentCreationEmail() {
    await this.send(
      "shipmentCreated",
      "New Shipment Created " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendShipmentEscalationEmail() {
    await this.send(
      "shipmentEscalation",
      "Escalation Email " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendDistributorAccoutActivationEmail() {
    await this.send("accountActivation", "Account Activation");
  }

  async sendPiGenerationEmail() {
    await this.send(
      "piGenerated",
      `Titan Order PI Generated ${this.emailPayload?.companyName || ""}_${
        this.emailPayload?.piNumber || ""
      }`
    );
  }

  async sendOrderCreateNotifyManagersEmail() {
    await this.send(
      "orderCreatedNotifyManagers",
      `Titan Order Placed_${
        this.emailPayload?.company || ""
      }_${currentMonth}-${currentYear}_${this.emailPayload?.orderID ?? ""}`
    );
  }

  async sendSlaBreachEmail() {
    await this.send(
      "slaBreach",
      "SLA Breach: Shipment Fulfillment Delay for Shipment " +
        (this.emailPayload.shipmentName || "")
    );
  }

  async sendLineSheetEmail() {
    await this.send("lineSheetEmail", "Titan D2D Product Catalog Export");
  }
}
