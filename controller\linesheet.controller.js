import ExcelJS from "exceljs";
import Inventory from "../model/inventory.model.js";
import {
  generateXlsxSheetForWebsite,
  generateXlsxSheetCdnLinksForWebsite,
  generateXlsxSheet,
} from "../utils/lineSheetFileDownload.js";
import Status from "../model/status.model.js";
import Distributor from "../model/distributor.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import Department from "../model/department.model.js";
import { getCountryManagerDistributors } from "./distributor.controller.js";
import { getCountryManagersOfDistributor } from "./action.controller.js";

export const alignmentPendingLineSheet = async (req, res) => {
  try {
    const shipments = await Status.aggregate([
      {
        $match: {
          pseudoId: "ALIGNMENT_PENDING",
        },
      },
      {
        $lookup: {
          from: "shipments",
          localField: "_id",
          foreignField: "status",
          as: "shipment",
        },
      },
      {
        $unwind: {
          path: "$shipment",
        },
      },
      {
        $project: {
          shipment: 1,
          _id: 0,
        },
      },
      {
        $lookup: {
          from: "orders",
          localField: "shipment.order",
          foreignField: "_id",
          as: "order",
        },
      },
      {
        $unwind: {
          path: "$order",
        },
      },
      {
        $lookup: {
          from: "distributors",
          localField: "order.distributor",
          foreignField: "_id",
          as: "distributor",
        },
      },
      {
        $unwind: {
          path: "$distributor",
        },
      },
    ]);

    if (!shipments.length) {
      console.log("No shipments found with ALIGNMENT_PENDING status.");
      return res
        .status(404)
        .json({ error: "No shipments found with ALIGNMENT_PENDING status." });
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Shipments");

    // Define the columns for the Excel sheet
    worksheet.columns = [
      { header: "Date", key: "date", width: 15 },
      { header: "SHIPMENT_ID", key: "shipment_id", width: 15 },
      { header: "SKU", key: "sku", width: 20 },
      { header: "Price", key: "price", width: 15 },
      { header: "Quantity", key: "quantity", width: 15 },
      { header: "Distributor", key: "distributor", width: 20 },
      { header: "Image", key: "image", width: 40 },
    ];

    shipments.forEach((shipment) => {
      shipment.shipment.lineItems?.forEach((item) => {
        worksheet.addRow({
          date: new Date(shipment.shipment.createdAt).toLocaleDateString(
            "en-GB"
          ),
          shipment_id: shipment.shipment.name,
          sku: item.sku,
          price: item.price,
          quantity: item.requested,
          distributor: shipment.distributor.name,
          image: item.image,
        });
      });
    });

    // Apply some formatting
    worksheet.getRow(1).font = { bold: true };
    worksheet.eachRow({ includeEmpty: false }, function (row, rowNumber) {
      row.height = 20;
    });

    res.setHeader(
      "Content-Disposition",
      'attachment; filename="Alignment_Pending_Shipments.xlsx"'
    );
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    await workbook.xlsx.write(res);

    res.end();

    console.log("Excel file generated: ALIGNMENT_PENDING.xlsx");
  } catch (error) {
    console.error("Error generating shipment excel:", error);
    res.status(500).send("Internal Server Error");
  }
};

export const generateLineSheet = async (req, res) => {
  const { customerId, brand, isImage } = req.body;

  try {
    if (!customerId) {
      res.status(400).json({
        responseCode: 1,
        status: "error",
        error: "Customer id is missing",
      });
    }
    // Fetch the specific distributor by ID
    const distributor = await Distributor.findOne({
      shopifyCustomerId: Number(customerId),
    });
    const query = {
      inventoryType: { $ne: "CUSTOM" },
      status: "ACTIVE",
    };

    // If brand is not 'all', apply the brand filter
    if (brand.toLowerCase() !== "all") {
      query.brand = brand;
    }

    const products = await Inventory.aggregate([
      { $match: query },
      {
        $addFields: {
          totalQuantity: {
            $sum: {
              $map: {
                input: "$sapSkus",
                as: "sku",
                in: "$$sku.quantity",
              },
            },
          },
        },
      },
      {
        $match: {
          $nor: [
            {
              $and: [{ pmrType: /DROP/i }, { totalQuantity: { $eq: 0 } }],
            },
          ],
        },
      },
    ]);

    if (isImage) {
      res.send({
        message:
          "File generation is in progress. You will be notified once it's ready.",
        status: "processing",
      });

      // Step 2: Start the long-running task asynchronously in the background
      (async () => {
        try {
          const sheet = await generateXlsxSheet(
            products,
            process.env.AWS_S3_BUCKET,
            brand,
            customerId
          );
          const recipients = [
            {
              name: `${distributor.firstName} ${distributor.lastName}`,
              email: distributor.email,
            },
          ];

          const ccCountryManagers = await getCountryManagersOfDistributor(
            distributor._id
          );

          console.log(sheet.downloadUrl);

          const ccCountryManagersEmails =
            ccCountryManagers?.map((x) => x.email) || [];

          // After sheet generation is complete, create the email notification
          await EmailNotification.create({
            emailCategory: "SHIPMENT",
            emailType: "LINE_SHEET",
            reciepient: recipients,
            cc: ccCountryManagersEmails,
            emailPayload: {
              userName: `${distributor.firstName} ${distributor.lastName}`,
              fileType:
                brand.toLowerCase() === "all"
                  ? "Full Stock File"
                  : `${brand} Stock File`,
              downloadLink: sheet.downloadUrl,
            },
          });
        } catch (error) {
          console.error(
            "Error generating the sheet or sending the email:",
            error
          );
        }
      })();
    } else {
      // Step 1: Directly generate the file using `generateXlsxSheetCdnLinksForWebsite` method
      const excelBuffer = await generateXlsxSheetCdnLinksForWebsite(
        products,
        customerId
      );

      // Step 2: Set headers for file download and send the file immediately
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="products.xlsx"'
      );

      // Send the buffer as the response
      res.send(excelBuffer);

      // No email notification here — just return without doing anything further
    }
  } catch (error) {
    console.error("Error sending Excel file:", error);
    res
      .status(500)
      .send("An error occurred while generating or sending the Excel file.");
  }
};
