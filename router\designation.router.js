import express from "express";
import {
  getDesignations,
  createDesignation,
  getOneDesignation,
  updateDesignation,
  deleteDesignation,
  updateDesignationHierarchy,
  checkExistingDesignation,
} from "../controller/designation.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("designation", "read"), getDesignations)
  .post(
    authenticateAdminPeopleAccess("designation", "write"),
    checkExistingDesignation,
    createDesignation
  );

router
  .route("/hierarchy/update")
  .patch(
    authenticateAdminPeopleAccess("designation", "write"),
    updateDesignationHierarchy
  );

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("designation", "read"), getOneDesignation)
  .patch(
    authenticateAdminPeopleAccess("designation", "write"),
    checkExistingDesignation,
    updateDesignation
  )
  .delete(
    authenticateAdminPeopleAccess("designation", "delete"),
    deleteDesignation
  );

export default router;
