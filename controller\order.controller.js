import Department from "../model/department.model.js";
import Distributor from "../model/distributor.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import AppError from "../utils/appError.js";
import catchAsync from "../utils/catchAsync.js";
import { deleteOne, getOne, updateOne } from "../utils/controllerFactory.js";
import Shipment from "../model/shipment.model.js";
import Cart from "../model/cart.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import fs from "fs";
import xlsx from "xlsx";
import path from "path";
import {
  uploadToS3,
  formattedMonthAndYear,
  getCountry,
} from "../utils/helperFunction.js";
import Inventory from "../model/inventory.model.js";
import Company from "../model/company.model.js";
import mongoose from "mongoose";
import {
  getCountryManagersOfDistributor,
  getDepartmentsPeoples,
} from "./action.controller.js";

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

export const getTheOrder = async (orderId) => {
  try {
    const order = await Order.findOne({ _id: orderId }).populate().lean();
    return order;
  } catch (err) {
    console.log(" Error in getting the order", err);
  }
};

export const getTheOrderNumber = async (orderId) => {
  try {
    const order = await getTheOrder(orderId);

    if (order) {
      const orderCreatedAt = order.createdAt;
      const orderNumber = order.name;
      const formattedDate = formattedMonthAndYear(orderCreatedAt);
      const distributerCountry = getCountry(order.distributor);
      return `OD - ${orderNumber}/${distributerCountry}/${formattedDate}`;
    } else {
      return;
    }
  } catch (err) {
    console.log("Error occured while getting the order number", err);
  }
};

export const generateOrderAttributes = async (shopifyCompanyId) => {
  const distributor = await Distributor.findOne({
    shopifyCompanyId: shopifyCompanyId,
  });
  const allShipmentStatus = await Status.find({ statusType: "Shipment" });
  const initialOrderStatus = await Status.find({
    statusType: "Order",
    isInitialStatus: true,
  });

  const departmentTypeIds = [...allShipmentStatus, ...initialOrderStatus].map(
    (shipmentStatus) => {
      return shipmentStatus.departmentType._id;
    }
  );

  const departmentPeoples = await Department.find({
    departmentType: { $in: departmentTypeIds },
    distributor: distributor._id,
  });

  const shipmentAttributesValues = allShipmentStatus.map((status) => {
    const departmentPeople = departmentPeoples.find(
      (it) =>
        it.departmentType._id.toString() == status.departmentType._id.toString()
    );

    return {
      category: "Shipment",
      statusId: status._id,
      status: status.status,
      isInitialStatus: status.isInitialStatus,
      escalationTriggerPeriod: status.escalationTriggerPeriod,
      emailInfo: [
        {
          name: departmentPeople?.name,
          email: departmentPeople?.email,
        },
      ],
    };
  });
  const orderAttributesValues = initialOrderStatus.map((status) => {
    const departmentPeople = departmentPeoples.find(
      (it) =>
        it.departmentType._id.toString() ==
        status.departmentType.map((dept) => dept._id.toString())
    );

    return {
      category: "Order",
      statusId: status._id,
      status: status.status,
      isInitialStatus: status.isInitialStatus,
      escalationTriggerPeriod: status.escalationTriggerPeriod,
      emailInfo: [
        {
          name: departmentPeople?.name,
          email: departmentPeople?.email,
        },
      ],
    };
  });
  const attributes = {
    name: "Send Notification Mail",
    type: "SEND_EMAIL",
    value: [...shipmentAttributesValues, ...orderAttributesValues],
  };
  return [attributes];
};

export const updateOrderAttribute = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const attributeId = req.params.attributeId;
  // const fieldId = req.params.fieldId;
  const attributes = req.body.attributes;
  if (!attributes)
    return next(
      new AppError("Missing attributes key in payload, example attributes[{}]")
    );
  const setOperations = {}; // To store $set operations
  const arrayFilters = []; // To store array filters
  attributes.forEach((fieldsToUpdate) => {
    const filterName = `attribute_${fieldId
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "")}`;
    // Unique name for array filter
    arrayFilters.push({ [`${filterName}.attributeId`]: attributeId }); // Define array filter
    // Create $set operations for each key-value pair in the update
    Object.entries(fieldsToUpdate).forEach(([key, value]) => {
      setOperations[`attribute.$[${filterName}].${key}`] = value; // Dynamic $set operation
    });
  });
  const updateResult = await Order.updateOne(
    { _id: orderId }, // Find the document by ID
    { $set: setOperations }, // Apply the set operations
    { arrayFilters } // Apply the defined array filters
  );
  res.status(200).json({
    status: "success",
    data: {
      data: updateResult,
    },
  });
});

export const deleteOrderAttribute = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const attributeId = req.params.attributeId;
  const updateResult = await Order.updateOne(
    { _id: orderId },
    { $pull: { attributes: { attributeId: attributeId } } }
  );
  res.status(200).json({
    status: "success",
    data: updateResult,
  });
});

export const getOrderByStatus = async (req, res, next) => {
  try {
    // Step 1: Get all shipment status IDs
    const shipmentStatuses = await Status.find({ statusType: "Shipment" });
    const shipmentStatusIds = shipmentStatuses.map((s) => s._id);

    // Step 2: Build the filter
    const filter = {
      status: { $in: shipmentStatusIds },
    };

    // Step 3: If the user is a country manager, restrict by distributor IDs
    if (
      req.user?.designation?.isCountryManager &&
      Array.isArray(req.user.distributor)
    ) {
      const distributorIds = req.user.distributor.map((d) => d._id.toString());
      filter["distributor_data._id"] = { $in: distributorIds };
    }

    /* 
    
    - Filtering the shipment and projecting only the required fields
    - Finally it is grouping by the status field
    */

    const shipments = await Shipment.aggregate([
      {
        $match: filter,
      },

      {
        $project: {
          order: 1,
          status: 1,
          distributor_data: 1,
        },
      },

      {
        $lookup: {
          from: "status",
          foreignField: "_id",
          localField: "status",
          as: "statusInfo",
        },
      },

      {
        $unwind: {
          path: "$statusInfo",
        },
      },
      {
        $project: {
          orderId: "$order",
          status: "$statusInfo.status",
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          orderIds: { $push: "$orderId" },
        },
      },
      {
        $project: {
          _id: 0,
          status: "$_id",
          count: 1,
          orderIds: 1,
        },
      },
    ]);

    res.status(200).json({
      status: "success",
      data: {
        data: shipments,
      },
    });
  } catch (err) {
    next(err);
  }
};

export const editOrderFromSheet = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const orderData = await Order.findOne({ _id: orderId });
  if (orderData.status.status != "CREATED") {
    res.status(400).send({ message: "Processed order can not be updated" });
  }

  if (!req.file) return next(new AppError("No file attached"));

  const filePath = req.file.path;
  const workbook = xlsx.readFile(filePath);

  const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
  const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

  if (jsonData.length === 0) return next(new AppError("Sheet is Empty", 400));

  const hasDuplicates = checkForDuplicates(jsonData);
  if (hasDuplicates)
    return next(
      new AppError("Duplicate ProductCode entries are not allowed.", 400)
    );

  const orderWithInvalidQuantity = jsonData.filter(
    (order) => !(Number.isInteger(order.Quantity) && order.Quantity > 0)
  );

  const orderWithEmptySKU = jsonData.filter((order) => !order.ProductCode);

  if (orderWithEmptySKU.length > 0) {
    return next(new AppError("Some Items have invalid ProductCode", 400));
  }

  if (orderWithInvalidQuantity.length > 0) {
    return next(new AppError("Some Items have Invalid quantity", 400));
  }

  const allSkus = jsonData.map((x) => x.ProductCode);

  const itemsFromDB = await Inventory.find({
    sku: { $in: allSkus },
  });

  const jsonDataWithPrice = jsonData.map((item) => {
    const inventory = itemsFromDB.find((x) => x.sku === item.ProductCode);
    if (!inventory) {
      return next(new AppError(item.ProductCode + " is not available", 400));
    }
    delete inventory._id;
    delete inventory.quantity;
    return {
      shopifyVariantId: inventory.shopifyVariantId,
      shopifyProductId: inventory.shopifyProductId,
      productTitle: inventory.productTitle,
      variantTitle: inventory.variantTitle,
      image: inventory.image,
      quantity: item.Quantity,
      price: inventory.price,
      sku: inventory.sku,
    };
  });

  const updatedOrder = await Order.updateOne(
    { _id: orderId },
    { line_items: jsonDataWithPrice }
  );

  fs.unlinkSync(filePath);
  res.status(200).json({ updatedOrder, updatedLineItems: jsonDataWithPrice });
});

export const generateOrderSheet = async (req, res, next) => {
  const orderId = req.params.id;
  const uploadResponse = await generateOrderSheetService(orderId);
  res.status(200).send(uploadResponse);
};

export const generateOrderSheetService = async (orderId) => {
  const order = await Order.findOne({ _id: orderId });
  let sheetPayload = [];

  let sheetHeader = ["productTitle", "image", "quantity", "sku", "price"];

  sheetPayload.push(sheetHeader);

  order.line_items.forEach((lineItem) => {
    // const values = (Object.values(lineItem)).filter(value => {
    //   return !['price', 'variantTitle', 'shopifyVariantId', 'shopifyProductId', '_id'].includes(value);
    // });
    let filteredItem = [];
    sheetHeader.forEach((key) => {
      if (lineItem.hasOwnProperty(key)) {
        filteredItem.push(lineItem[key] ? lineItem[key] : "");
      } else {
        filteredItem.push("");
      }
    });
    sheetPayload.push(filteredItem);
    // sheetPayload.push(values);
  });

  sheetPayload[0] = ["Title", "Image", "Quantity", "ProductCode", "FOB"];

  const workbook = xlsx.utils.book_new();
  const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);

  xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

  const fileName = `OrderName-${order.name}-m1.xlsx`;

  const orderSheetPath = path.join(
    __dirname,
    "..",
    "asset",
    "pi",
    `${fileName}`
  );

  xlsx.writeFile(workbook, orderSheetPath);

  const uploadResponse = await uploadToS3(
    orderSheetPath,
    process.env.AWS_S3_BUCKET,
    fileName
  );

  fs.unlinkSync(orderSheetPath);
  return uploadResponse;
};

export const convertCartToOrder = async (req, res, next) => {
  const cartId = req.params.cartId;
  const { billing_address, shipping_address } = req.body;

  if (!billing_address || !shipping_address) {
    return next(
      new AppError(
        "Both billing_address and shipping_address are mandatory.",
        400
      )
    );
  }
  const cart = await Cart.findOne({ _id: cartId, status: "CREATED" });

  if (!cart) return next(new AppError(`Invalid Cart!`, 400));
  const initialOrderStatus = await Status.findOne({
    statusType: "Order",
    isInitialStatus: true,
  }).populate({
    path: "departmentNotified",
  });

  if (!initialOrderStatus)
    return next(
      new AppError("Missing Initial Order Status, Create Initial Order Status")
    );

  const originalDistributor = await Distributor.findOne({
    shopifyCustomerId: parseInt(cart.customer.shopifyCustomerId),
  });

  if (!originalDistributor) return next(new AppError("Customer is missing!"));

  const orderPayload = {
    shopifyCompanyId: cart.shopifyCompanyId,
    cartId: cart.cartId,
    status: initialOrderStatus._id,
    distributor: originalDistributor._id,
    customer: cart.customer,
    line_items: cart.line_items,
    shipping_address: shipping_address,
    billing_address: billing_address,
  };
  const createdOrder = await Order.create(orderPayload);
  const updatedCartStatus = await Cart.updateOne(
    { _id: cartId },
    { status: "COMPLETED" }
  );

  /* 
   Different Calculation of the order for sending in the email payload
  */

  const orderDetails = cart.line_items.reduce(
    (acc, current) => {
      return {
        ...acc,
        totalQuantity: acc.totalQuantity + current.quantity,
        totalPrice: acc.totalPrice + current.price * current.quantity,
      };
    },
    {
      totalQuantity: 0,
      totalPrice: 0,
    }
  );

  const ccRecipients = await getDepartmentsPeoples("SECONDARY_NOTIFICATION");

  /* 
   recipients of the country Manager and supply chain
  */

  const supplyChainRecipient = await getDepartmentsPeoples(
    "SUPPLY_CHAIN_OPERATIONS"
  );

  const countryManagerRecipient = await getCountryManagersOfDistributor(
    originalDistributor._id
  );

  const company = await Company.findOne({
    shopifyCompanyId: originalDistributor.shopifyCompanyId,
  });

  /* 
    Refactor: Replace the order number with the order name

    Implementation: 
    -Fetching the corresponding order number using getTheOderNumberFunction
  */

  const orderNumber = await getTheOrderNumber(createdOrder._id);

  if (supplyChainRecipient.length > 0) {
    await EmailNotification.create({
      emailCategory: "ORDER",
      emailType: "ORDER_CREATE",
      reciepient: supplyChainRecipient,
      cc: ccRecipients.map((x) => x.email) || [],
      emailPayload: {
        company: company.name,
        orderID: orderNumber,
        orderValue: parseFloat(orderDetails.totalPrice.toFixed(2)),
        orderQty: orderDetails.totalQuantity,
        date: createdOrder?.createdAt,
        distributorName: `${originalDistributor.firstName} ${
          originalDistributor?.lastName || ""
        }`,
        customerId: originalDistributor.shopifyCustomerId,
        cartProducts: createdOrder.line_items,
      },
    });
  }

  if (countryManagerRecipient.length > 0) {
    await EmailNotification.create({
      emailCategory: "ORDER",
      emailType: "ORDER_CREATE_NOTIFY_MANAGER",
      reciepient: [
        ...countryManagerRecipient,
        {
          name: `${originalDistributor.firstName} ${
            originalDistributor?.lastName || ""
          }`,
          email: originalDistributor.email,
        },
      ],
      cc: ccRecipients.map((x) => x.email) || [],
      emailPayload: {
        company: company.name,
        orderName: createdOrder.name,
        orderValue: parseFloat(orderDetails.totalPrice.toFixed(2)),
        orderQty: orderDetails.totalQuantity,
        orderID: orderNumber,
        date: createdOrder.createdAt,
        distributorEmail: originalDistributor.email,
        distributorName: `${originalDistributor.firstName} ${
          originalDistributor?.lastName || ""
        }`,
        customerId: originalDistributor.shopifyCustomerId,
        cartProducts: createdOrder.line_items,

        // orderStatus: initialOrderStatus.status,
      },
    });
  }

  res.status(200).send({
    status: "success",
    data: {
      data: createdOrder,
    },
  });
};

export const getOrdersForWebsite = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  let limit = parseInt(req.query.limit) || 10;
  limit = limit > 50 ? 50 : limit;
  const skip = (page - 1) * limit;

  const queryObj = { ...req.query };
  const excludedFields = ["page", "sort", "limit", "fields"];
  excludedFields.forEach((el) => delete queryObj[el]);

  let matchStage = { ...queryObj };

  const [aggregatedOrders, totalCountResult] = await Promise.all([
    Order.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      { $unwind: "$status" },
      {
        $project: {
          _id: 1,
          name: 1,
          status: {
            status: 1,
            colorCode: 1,
          },
          createdAt: 1,
          updatedAt: 1,
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ]),
    Order.aggregate([{ $match: matchStage }, { $count: "total" }]),
  ]);

  const totalCount =
    totalCountResult.length > 0 ? totalCountResult[0].total : 0;

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: aggregatedOrders,
    },
  });
});

export const getOneOrder = getOne(Order);
export const updateOrder = updateOne(Order);
export const deleteOrder = deleteOne(Order);

// check for duplicate SKU entries
export function checkForDuplicates(data) {
  const productCodeSet = new Set();
  for (let item of data) {
    if (item.sku) {
      if (productCodeSet.has(item.sku)) {
        return true;
      }
      productCodeSet.add(item.sku);
    }

    if (item.ProductCode) {
      if (productCodeSet.has(item.ProductCode)) {
        return true;
      }
      productCodeSet.add(item.ProductCode);
    }
  }
  return false;
}

export const getCountryManagerOrders = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  let limit = parseInt(req.query.limit) || 10;
  limit = limit > 50 ? 50 : limit;
  const skip = (page - 1) * limit;

  const queryObj = { ...req.query };
  const excludedFields = ["page", "sort", "limit", "fields"];
  excludedFields.forEach((el) => delete queryObj[el]);

  let matchStage = { ...queryObj };

  if (
    req.user?.designation?.isCountryManager &&
    Array.isArray(req.user.distributor)
  ) {
    const distributorIds = req.user.distributor.map((d) => d._id);
    matchStage["distributor"] = { $in: distributorIds };
  }

  const aggregatedOrders = await Order.aggregate([
    { $match: matchStage },
    {
      $lookup: {
        from: "status",
        localField: "status",
        foreignField: "_id",
        as: "status",
      },
    },
    { $unwind: "$status" },
    {
      $lookup: {
        from: "distributors",
        localField: "distributor",
        foreignField: "_id",
        as: "distributor",
      },
    },
    { $unwind: "$distributor" },
    {
      $project: {
        _id: 1,
        name: 1,
        status: {
          status: 1,
          colorCode: 1,
        },
        distributor: {
          firstName: 1,
          lastName: 1,
          name: 1,
          country: 1,
        },
        line_items: {
          $map: {
            input: "$line_items",
            as: "item",
            in: {
              quantity: "$$item.quantity",
              price: "$$item.price",
            },
          },
        },
        createdAt: 1,
        updatedAt: 1,
      },
    },

    {
      $facet: {
        results: [
          { $sort: { createdAt: -1 } },
          { $skip: skip },
          { $limit: limit },
        ],
        totalCount: [{ $count: "count" }],
      },
    },
    {
      $project: {
        results: 1,
        totalCount: { $arrayElemAt: ["$totalCount.count", 0] },
      },
    },
  ]);

  let data = {
    totalCount: 0,
    results: [],
  };

  if (aggregatedOrders.length > 0) {
    data.results = aggregatedOrders[0].results;
    data.totalCount = aggregatedOrders[0].totalCount;
  }

  res.status(200).json({
    status: "success",
    result: data.totalCount,
    data: {
      data: data.results,
    },
  });
});
