import mongoose from "mongoose";

const linesheetSchema = new mongoose.Schema(
  {
    distributorID: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Distributor",
      required: true,
    },
    URLs: [
      {
        brandName: { type: String, required: true },
        downloadUrl: { type: String, required: true },
      },
    ],
  },
  { timestamps: true }
);

const LinesheetRecord = mongoose.model("LinesheetRecord", linesheetSchema);
export default LinesheetRecord;
