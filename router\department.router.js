import express from "express";
import {
  getDepartments,
  createDepartment,
  getOneDepartment,
  updateDepartment,
  deleteDepartment,
  loginDepartmentPeople,
  hashPassword,
} from "../controller/department.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";
import decryption from "../service/encryptDecrypt/decryption.js";
const router = express.Router();

router.route("/login").post(decryption, loginDepartmentPeople);

router
  .route("/")
  .get(authenticateAdminPeopleAccess("deptt_people", "read"), getDepartments)
  .post(
    authenticateAdminPeopleAccess("deptt_people", "write"),
    createDepartment
  );

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("deptt_people", "read"), getOneDepartment)
  .patch(
    authenticateAdminPeopleAccess("deptt_people", "write"),
    hashPassword,
    updateDepartment
  )
  .delete(
    authenticateAdminPeopleAccess("deptt_people", "delete"),
    deleteDepartment
  );

export default router;
