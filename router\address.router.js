import express from "express";
import {
  getAddresses,
  createAddress,
  getOneAddress,
  updateAddress,
  deleteAddress,
} from "../controller/distributorAddress.controller.js";
import {
  createCustomerAddressShopify,
  deleteCustomerAddressShopify,
  updateCustomerAddressShopify,
} from "../controller/distributor.controller.js";

const router = express.Router();

router
  .route("/")
  .get(getAddresses)
  .post(createCustomerAddressShopify, createAddress);

router
  .route("/:id")
  .get(getOneAddress)
  .patch(updateCustomerAddressShopify, updateAddress)
  .delete(deleteCustomerAddressShopify, deleteAddress);

export default router;
