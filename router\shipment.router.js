import express from "express";
import {
  getShipment,
  createShipment,
  getOneShipment,
  updateShipment,
  deleteShipment,
  updateShipmentStatus,
  returnShipmentDataWithActions,
  downloadShipmentExcel,
  editShipment,
  editShipmentWithSla,
  generateShipmentPISheet,
  allocateInventoryManually,
  sendOrderDispatchedEmail,
} from "../controller/shipment.controller.js";
import { notifyStatusChange } from "../controller/common/index.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";
import { alignmentPendingOrderProcess } from "../controller/action.controller.js";
// import { downloadShipmentExcel, uploadShipmentExcel } from '../../controller/shipment/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

const router = express.Router();
const setStatusUpdateType = async (req, res, next) => {
  req.body.middlewareStatusCategory = "Shipment";
  next();
};
router
  .route("/")
  .get(authenticateDepttPeopleAccess("shipment", "read"), getShipment)
  .post(
    setStatusUpdateType,
    authenticateDepttPeopleAccess("shipment", "write"),
    createShipment
  );

router
  .route("/generate_pi")
  .get(
    authenticateDepttPeopleAccess("shipment", "read"),
    generateShipmentPISheet
  );

router
  .route("/download_shipment_items")
  .get(
    authenticateDepttPeopleAccess("shipment", "read"),
    downloadShipmentExcel
  );

router
  .route("/:shipmentId/send-email")
  .post(
    authenticateDepttPeopleAccess("shipment", "write"),
    sendOrderDispatchedEmail
  );

router
  .route("/:shipmentId/edit")
  .post(
    upload.single("file"),
    authenticateDepttPeopleAccess("shipment", "write"),
    editShipment
  );

router
  .route("/:shipmentId/pending_sla_upload")
  .post(
    upload.single("file"),
    authenticateDepttPeopleAccess("shipment", "write"),
    editShipmentWithSla
  );

router
  .route("/:id")
  .get(
    authenticateDepttPeopleAccess("shipment", "read"),
    returnShipmentDataWithActions
  )
  // .patch(setStatusUpdateType, updateShipment)
  .patch(
    authenticateDepttPeopleAccess("shipment", "write"),
    setStatusUpdateType,
    notifyStatusChange,
    updateShipment
  )
  .delete(authenticateDepttPeopleAccess("shipment", "delete"), deleteShipment);

router
  .route("/:id/status")
  .post(
    authenticateDepttPeopleAccess("shipment", "write"),
    updateShipmentStatus
  );

router
  .route("/manually_inventory_allocation")
  .post(
    authenticateDepttPeopleAccess("shipment", "write"),
    allocateInventoryManually
  );

export default router;
