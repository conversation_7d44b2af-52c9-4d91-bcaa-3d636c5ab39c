import express from "express";
import {
  getTimeline,
  createTimeline,
  getOneTimeline,
  updateTimeline,
  deleteTimeline,
} from "../controller/timeline.controller.js";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateDepttPeopleAccess("timeline", "read"), getTimeline)
  .post(authenticateDepttPeopleAccess("timeline", "write"), createTimeline);

router
  .route("/:id")
  .get(authenticateDepttPeopleAccess("timeline", "read"), getOneTimeline)
  .patch(authenticateDepttPeopleAccess("timeline", "write"), updateTimeline)
  .delete(authenticateDepttPeopleAccess("timeline", "delete"), deleteTimeline);

export default router;
