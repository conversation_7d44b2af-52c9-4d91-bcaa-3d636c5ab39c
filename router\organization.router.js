import express from "express";
import {
  getOrganizations,
  createOrganization,
  getOneOrganization,
  updateOrganization,
  deleteOrganization,
} from "../controller/organization.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("organization", "read"), getOrganizations)
  .post(
    authenticateAdminPeopleAccess("organization", "write"),
    createOrganization
  );

router
  .route("/:id")
  .get(
    authenticateAdminPeopleAccess("organization", "read"),
    getOneOrganization
  )
  .patch(
    authenticateAdminPeopleAccess("organization", "write"),
    updateOrganization
  )
  .delete(
    authenticateAdminPeopleAccess("organization", "delete"),
    deleteOrganization
  );

export default router;
