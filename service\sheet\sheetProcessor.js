import xlsx from "xlsx";
import mongoose from "mongoose";

mongoose
  .connect(`${process.env.MONGO_DB_URL}`)
  .then(async (con) => {
    console.log("DB connection successfull");
  })
  .catch((error) => {
    console.log(error.name, error.message);
  });

process.on("message", async ({ filePath }) => {
  try {
    const workbook = xlsx.readFile(filePath);

    const data = workbook.SheetNames.map((sheetName) => {
      const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);
      return {
        sheetName,
        jsonData,
      };
    });

    process.send({ data });
  } catch (err) {
    console.error(" Error occured while processing the xlsx file", err);
    throw new Error(" Error occured while processing the xlsx file", err);
  }
});
