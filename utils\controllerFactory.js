import catchAsync from "./catchAsync.js";
import AppError from "./appError.js";
import APIFeatures from "./apiFeatures.js";
import Shipment from "../model/shipment.model.js";
import Distributor from "../model/distributor.model.js";
import Designation from "../model/designation.model.js";
import mongoose from "mongoose";

export const getAll = (Model) =>
  catchAsync(async (req, res, next) => {
    const features = new APIFeatures(Model.find({}, { password: 0 }), req.query)
      .filter()
      .sort()
      .limitFields()
      .paginate();
    const doc = await features.query;
    res.status(200).json({
      status: "success",
      result: await Model.countDocuments(),
      data: {
        data: doc,
      },
    });
  });

export const getOne = (Model, populateOption) =>
  catchAsync(async (req, res, next) => {
    const id = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(new AppError("Provided Id is not valid."));
    }
    let query = Model.findById(id).lean();
    if (populateOption) query = query.populate(populateOption);
    const doc = await query;
    if (!doc) return next(new AppError("No document found with this ID"));
    res.status(200).json({
      status: "success",
      data: {
        data: doc,
      },
    });
  });

export const createOne = (Model) =>
  catchAsync(async (req, res, next) => {
    if (
      req.body.departmentNotified &&
      Array.isArray(req.body.departmentNotified)
    ) {
      req.body.departmentNotified = req.body.departmentNotified.map(
        (item) => item.value
      );
    }
    const doc = await Model.create(req.body);
    res.status(201).json({
      status: "success",
      result: 1,
      data: {
        data: doc,
      },
    });
  });

export const updateOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const id = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(new AppError("Provided Id is not valid."));
    }
    const doc = await Model.findByIdAndUpdate(id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      status: "success",
      result: 1,
      data: {
        data: doc,
      },
    });
  });

export const deleteOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const { distributor, designation } = req.body;

    if (distributor && designation) {
      const countryManager = req.body._id;

      const foundDesignation = await Designation.findById(designation);
      if (foundDesignation && foundDesignation.isCountryManager) {
        for (const distributorId of distributor) {
          const foundDistributor = await Distributor.findById(distributorId);

          if (foundDistributor) {
            foundDistributor.countryManager =
              foundDistributor.countryManager.filter(
                (id) => id.toString() !== countryManager.toString()
              );
            await foundDistributor.save();
          }
        }
      }
    }

    const doc = await Model.findByIdAndDelete(req.params.id);
    if (!doc) return next(new AppError("No document found with this ID", 400));
    res.status(204).json({
      status: "success",
      data: "Data deleted successfully",
    });
  });
