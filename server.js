import * as dotenv from "dotenv";
dotenv.config();
import mongoose from "mongoose";
import path from "path";
import SftpClient from "ssh2-sftp-client";

import app from "./app.js";

const PORT = process.env.PORT || 3600;

const sftpConfig = {
  host: "************",
  username: "DDrive",
  port: "22",
  password: "JkI404$ha83#08",
  remoteDir: "/home/<USER>/DEV/Indent/Process",
};

export const getFileDetailsFromSftp = async () => {
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    console.log("connected");
    console.log(
      await sftp.list("/home/<USER>/DEV/Indent/Process"),
      "success list"
    );
    return await sftp.list("/home/<USER>/DEV/Indent/Process");
  } catch (error) {
    await sftp.end();
    console.log(error, "catch");
    return error;
  } finally {
    await sftp.end();
  }
};

mongoose
  .connect(`${process.env.MONGO_DB_URL}`)
  .then(async (con) => {
    console.log("log before function call");
    //await getFileDetailsFromSftp()
    console.log("log after function call");

    console.log("Main DB connection Successful");
    // console.log(await Inventory.deleteMany({}))
    // await uploadToS3(filePath, 'titan-d2d')
    // generateEscalationNotification()
  })
  .catch((error) => {
    console.log(error);
  });

app.listen(PORT, () => {
  console.log(`Server started on ${PORT}`);
});
