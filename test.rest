GET http://localhost:3600/api/order


### Create User 
POST http://localhost:3600/api/user
Content-Type: application/json

{
    "name":"<PERSON>h Bajaj",
    "username":"<EMAIL>",
    "password":"test123",
    "userGroup":["663b0f8c8ac71a1dbd8bf942"]
}

### Update User 
PATCH  http://localhost:3600/api/user/663b2c9f982735a59fcc33d1
Content-Type: application/json

{
    "name":"<PERSON><PERSON><PERSON><PERSON><PERSON> new",
    "username":"<EMAIL>"
}

### Login User 
POST http://localhost:3600/api/user/login
Content-Type: application/json

{
  "username":"<EMAIL>",
  "password":"test123"
}

### Delete User
DELETE http://localhost:3600/api/user/663b2c9f982735a59fcc33d1


### Create User Group 
POST http://localhost:3600/api/usergroup
Content-Type: application/json

{
   "name":"Super Admin",
    "access_scopes":{
      "department": ["read", "write", "delete"],
      "designation": ["read", "write", "delete"],
      "distributor": ["read", "write", "delete"],
      "organization": ["read", "write", "delete"],
      "order": ["read", "write", "delete"],
      "shipment": ["read", "write", "delete"],
      "status": ["read", "write", "delete"],
      "statusFlow": ["read", "write", "delete"],
      "user": ["read", "write", "delete"],
      "user_group": ["read", "write", "delete"]
  }
}
 
### Delete User group
DELETE http://localhost:3600/api/usergroup/663b0f8c8ac71a1dbd8bf942


### Get Action
GET http://localhost:3600/api/action


### Create Action
POST http://localhost:3600/api/action
Content-Type: application/json

{
  "action_name":"Set SLA for Pending Shipment",
  "route": "/api/shipment/:shipmentId/pending_sla_upload",
  "action_unique_key":"pending_sla_upload"
}



### Get Status Action Link
GET http://localhost:3600/api/status_action_link/663c4cca864c679bd21974c7


### Create status_action_link
POST http://localhost:3600/api/status_action_link
Content-Type: application/json

{
      "action_id":"6644bc4076018dd667001682",
      "action_display_name": "Edit Shipment Item Quantities",
      "current_status_id":"663c4b4e864c679bd21974b0",
      "next_status_id": "663c4b27864c679bd21974ac",
      "formData": [
        {
          "input_name": "Upload updated Sheet",
          "input_type": "input",
          "value_type": "file",
          "is_mandatory": true,
          "input_box":"file"
        }
      ]
}

### DELETE Action
DELETE http://localhost:3600/api/status_action_link/663c4c4f864c679bd21974bf


### Create Status
POST http://localhost:3600/api/status2
Content-Type: application/json

{
  "status": "Waiting for Allocation(with SLA)",
  "isInitialStatus": false,
  "statusType": "Shipment",
  "departmentType":  "6644bcc420c12c4d4290b214",
  "escalationTriggerPeriod": 10,
  "colorCode": "#a92222",
  "pseudoId": "allocation_pending_with_sla"
}


### Get Shipment
GET http://localhost:3600/api/shipment/663803befd5805a629133c8f


### trigger change Status Action
POST http://localhost:3600/api/action/change_status
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************.2b96cqm0i1MeXcGApjYTMwTe0d3P0k75nPKxuN-XSlc

{
  "action_link_id": "663c4c17864c679bd21974bc",
  "shipment_id":"6644d14c582ae9f3c662fd94",
  "form_data":[{
    "input_id" : "663c4c17864c679bd21974bd",
    "value" : "************"
  }]
}


### Create people  (department)
POST http://localhost:3600/api/department_people
Content-Type: application/json

{
    "name": "Abhi",
    "email": "<EMAIL>",
    "country": "India",
    "reportingTo": "ABC",
    "departmentType": "6637f934622d0e63dc195b86",
    "designation": "6637fbae43e400310a85649d",
    "distributor": "6637fa5e622d0e63dc195b90",
    "password":"test123"
}

### Login people  (department)
POST https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/department_people/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password":"test123"
}

### GET people  (department)
GET http://localhost:3600/api/department_people/663ddd1c5e505affef178acb

### Download linesheet  (xlsx)
GET http://localhost:3600/api/linesheet/download

### trigger order batch processing
GET http://localhost:3600/api/action/process_orders
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.w71DbvaeTNz_olV9Xojeb3_DkAsfLx4xWrjTSycXjDk



### trigger edit aligned shipment
POST http://localhost:3600/api/action/change_status
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.w71DbvaeTNz_olV9Xojeb3_DkAsfLx4xWrjTSycXjDk

{
  "action_link_id": "663c4c92864c679bd21974c4",
  "shipment_id":"6644bd8320c12c4d4290b251",
}

### pmrStatus
GET http://localhost:3600/api/pmrStatus
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.w71DbvaeTNz_olV9Xojeb3_DkAsfLx4xWrjTSycXjDk

### pmrStatus
GET http://localhost:3600/api/inventory/abhi