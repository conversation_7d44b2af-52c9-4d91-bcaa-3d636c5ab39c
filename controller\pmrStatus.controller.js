import Inventory from "../model/inventory.model.js";
import Order from "../model/order.model.js";
import Shipment from "../model/shipment.model.js";

export const calculatePMRType = async (req, res) => {
  try {
    const currentDate = new Date();
    const pastDate = new Date();
    pastDate.setDate(currentDate.getDate() - 30);

    const orderStatusAggregation = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: pastDate, $lte: currentDate },
        },
      },
      {
        $lookup: {
          from: "status", // Assuming you have a 'statuses' collection
          localField: "status", // Field in the Order model that references the status
          foreignField: "_id", // Field in the statuses collection that matches
          as: "statusDetails",
        },
      },
      {
        $unwind: "$statusDetails",
      },
      {
        $match: {
          "statusDetails.pseudoId": {
            $in: ["IN_PROGRESS", "CREATED"],
          },
        },
      },
      {
        $group: {
          _id: "$statusDetails.pseudoId", // Grouping by the pseudoId of the status
          count: { $sum: 1 }, // Counting the number of orders for each status
        },
      },
      {
        $sort: { count: -1 }, // Optional: Sorting statuses in descending order by count
      },
    ]);

    const shipmetAggregation = await Shipment.aggregate([
      {
        $match: {
          createdAt: { $gte: pastDate, $lte: currentDate }, // Filter shipments from the last 3 days
        },
      },
      {
        $lookup: {
          from: "status", // Assuming you have a 'status' collection
          localField: "status", // Field in the Shipment model that references the status
          foreignField: "_id", // Matching field in the status collection
          as: "statusDetails",
        },
      },
      {
        $unwind: {
          path: "$statusDetails",
          preserveNullAndEmptyArrays: true, // To include shipments without any status
        },
      },
      {
        $facet: {
          allShipments: [
            {
              $group: {
                _id: null, // Grouping all shipments together
                count: { $sum: 1 }, // Counting all shipments
              },
            },
          ],
          shipmentCancelled: [
            {
              $match: {
                "statusDetails.pseudoId": "SHIPMENT_CANCELLED", // Filter for cancelled shipments
              },
            },
            {
              $group: {
                _id: "$statusDetails.pseudoId", // Group by the status
                count: { $sum: 1 }, // Count the cancelled shipments
              },
            },
          ],
        },
      },
      {
        $project: {
          allShipments: { $arrayElemAt: ["$allShipments.count", 0] },
          shipmentCancelled: { $arrayElemAt: ["$shipmentCancelled.count", 0] },
        },
      },
    ]);

    console.log(shipmetAggregation, "opopopopop");

    // Transform the result to an array with specific headings
    const resultArray = [
      {
        heading: "Total Orders",
        count: orderStatusAggregation
          .filter(
            (item) => item._id === "IN_PROGRESS" || item._id === "CREATED"
          )
          .reduce((acc, item) => acc + item.count, 0),
      },
      {
        heading: "Shipment Created",
        count: shipmetAggregation[0].allShipments || 0,
      },
      {
        heading: "Shipment Canceled",
        count: shipmetAggregation[0].shipmentCancelled || 0,
      },
    ];

    return res.status(200).json(resultArray);
  } catch (error) {
    console.error(
      "Error calculating specific status orders in the past 30 days:",
      error
    );
    res.status(500).json({
      error: "Failed to calculate specific status orders in the past 30 days.",
    });
  }
};
