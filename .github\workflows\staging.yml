name: Deploy Titan-d2d staging - Backend

on:
  push:
    branches: [current-staging]

jobs:
  deploy:
    runs-on: ubuntu-latest # GitHub-hosted runner

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🛠 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 🔐 Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.STC_AWS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.STC_AWS_HOST_IP }} >> ~/.ssh/known_hosts

      - name: 🚀 SSH Deploy Backend
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.STC_AWS_USERNAME }}@${{ secrets.STC_AWS_HOST_IP }} << 'EOF'
            set -e
            cd /home/<USER>/titan-d2d-backend
            touch .env
            echo "${{ secrets.STAGING_ENV }}" > .env
            git pull origin current-staging
            npm i
            pm2 restart staging-backend
            pm2 save
          EOF
