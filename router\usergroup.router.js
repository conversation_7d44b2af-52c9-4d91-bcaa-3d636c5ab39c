import express from "express";

import {
  getUserGroups,
  getOneUserGroup,
  createUserGroup,
  updateUserGroup,
  deleteUserGroup,
} from "../controller/usergroup.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("user_group", "read"), getUserGroups)
  .post(authenticateAdminPeopleAccess("user_group", "write"), createUserGroup);

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("user_group", "read"), getOneUserGroup)
  .patch(authenticateAdminPeopleAccess("user_group", "write"), updateUserGroup)
  .delete(
    authenticateAdminPeopleAccess("user_group", "delete"),
    deleteUserGroup
  );

export default router;
