import mongoose from "mongoose";

const inventorySchema = new mongoose.Schema(
  {
    shopifyVariantId: {
      type: String,
    },
    productCategory: {
      type: String,
    },
    inventoryType: {
      type: String,
      enum: ["CUSTOM", "STANDARD"],
      default: "STANDARD",
    },
    shopifyProductId: {
      type: String,
    },
    productTitle: {
      type: String,
    },
    variantTitle: {
      type: String,
    },
    price: {
      type: String,
    },
    sku: {
      type: String,
      index: true,
      unique: true,
    },
    brand: {
      type: String,
    },
    cluster: {
      type: String,
    },
    hsnCode: {
      type: String,
    },
    caseMaterial: {
      type: String,
    },
    caseWidth: {
      type: String,
    },
    caseLength: {
      type: String,
    },
    caseThickness: {
      type: String,
    },
    strapMaterial: {
      type: String,
    },
    dialColor: {
      type: String,
    },
    function: {
      type: String,
    },
    pmrType: {
      type: String,
    },
    quantity: {
      type: Number,
      required: true,
    },
    on_hold: {
      type: Number,
    },
    skuType: {
      type: String,
    },
    image: {
      type: String,
    },
    pmrStatus: {
      type: String,
    },
    brand: {
      type: String,
    },
    cluster: {
      type: String,
    },
    gender: {
      type: String,
    },
    collection: {
      type: String,
    },
    status: {
      type: String,
    },
    priceList: [
      {
        price: String,
        currency: String,
        companyLocationCatalogId: String,
      },
    ],
    sapSkus: [
      {
        sku: String,
        eanNo: String,
        quantity: {
          type: Number,
          default: 0,
        },
        onHold: {
          type: Number,
          default: 0,
        },
      },
    ],
  },
  { timestamps: true }
);

const Inventory = mongoose.model("Inventory", inventorySchema);
export default Inventory;
