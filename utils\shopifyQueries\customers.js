/* 
  Shopify Queries to the get the customer details

*/

export const customerQueries = {
  /* 
    @params (string) customerId: this will be shopify gid of the customer.
  */
  getCustomer: (customerId) => ({
    query: `query getCustomer($customerId: ID!) {
            customer(id: $customerId) {
              id
              firstName
              lastName
              email
              phone
              tags
              lifetimeDuration
              canDelete
              payment_terms: metafield(namespace:"custom" ,key:"payment_terms"){
                value
              }
              source:metafield(namespace:"custom" ,key:"source"){
                value
              }
              inco_terms:metafield(namespace:"custom" ,key:"inco_terms"){
                value
              }  
              shipping_instruction:metafield(namespace:"custom" ,key:"shipping_instruction"){
                value
              }  
              delivery: metafield(namespace:"custom" ,key:"delivery"){
                value
              }  
              partshipment:metafield(namespace:"custom" ,key:"partshipment"){
                value
              }  
              port_of_loading:metafield(namespace:"custom" ,key:"port_of_loading"){
                value
              }  
              port_of_discharge:metafield(namespace:"custom" ,key:"port_of_discharge"){
                value
              }    
             
            }
          }
        `,
    variables: {
      customerId: customerId,
    },
  }),
};
