import Department from "../model/department.model.js";
import Shipment from "../model/shipment.model.js";
import Status from "../model/status.model.js";
import Inventory from "../model/inventory.model.js";
import StatusActionLink from "../model/statusActionLink.model.js";
import Order from "../model/order.model.js";
import Company from "../model/company.model.js";
import {
  alignmentPendingOrderProcessTrigger,
  endEscalationStatus,
  getCountryManagersOfDistributor,
  getDepartmentsPeoples,
  onlyUniqueRecipients,
} from "./action.controller.js";
import fs from "fs";
import {
  createOne,
  deleteOne,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import mongoose from "mongoose";
import xlsx from "xlsx";
import AppError from "../utils/appError.js";
import path, { dirname } from "path";
import {
  formattedMonthAndYear,
  generateSignedURL,
  uploadToS3,
} from "../utils/helperFunction.js";
import { generateInvoicePdf } from "../utils/pdfGenerator.js";
import Distributor from "../model/distributor.model.js";
import { getDepartmentNotifiedEmails } from "../utils/findNotifiedDepartment.js";
import PISheet from "../model/piSheet.model.js";
import { fileURLToPath } from "url";
import axios from "axios";
import { customerQueries } from "../utils/shopifyQueries/customers.js";
import { Email } from "../service/rule/index.js";
import catchAsync from "../utils/catchAsync.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ObjectId = mongoose.Types.ObjectId;

export const getShipment = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  let limit = parseInt(req.query.limit) || 10;
  limit = limit > 50 ? 50 : limit;
  const skip = (page - 1) * limit;

  const queryObj = { ...req.query };
  const excludedFields = ["page", "sort", "limit", "fields"];
  excludedFields.forEach((el) => delete queryObj[el]);

  if (queryObj.order) {
    queryObj.order = new mongoose.Types.ObjectId(queryObj.order);
  }

  if (queryObj.status) {
    queryObj.status = new mongoose.Types.ObjectId(queryObj.status);
  }

  let matchStage = { ...queryObj };

  if (
    req.user?.designation?.isCountryManager &&
    Array.isArray(req.user.distributor)
  ) {
    const distributorIds = req.user.distributor.map((d) => d._id.toString());
    matchStage["distributor_data._id"] = { $in: distributorIds };
  }

  const [aggregatedOrders, totalCountResult] = await Promise.all([
    Shipment.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      { $unwind: "$status" },
      {
        $project: {
          _id: 1,
          name: 1,
          status: {
            status: 1,
            colorCode: 1,
            pseudoId: 1,
          },
          distributor_data: 1,
          createdAt: 1,
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ]),
    Shipment.aggregate([{ $match: matchStage }, { $count: "total" }]),
  ]);

  const totalCount =
    totalCountResult.length > 0 ? totalCountResult[0].total : 0;

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: aggregatedOrders,
    },
  });
});

export const getOneShipment = getOne(Shipment);
export const createShipment = createOne(Shipment);
export const updateShipment = updateOne(Shipment);
export const updateShipmentStatus = updateOne(Shipment);
export const deleteShipment = deleteOne(Shipment);

export const month = [
  "JAN",
  "FEB",
  "MAR",
  "APR",
  "May",
  "JUN",
  "JUL",
  "AUG",
  "SEP",
  "OCT",
  "NOV",
  "DEC",
];

function calculatePercentageChange(current, previous) {
  if (previous === 0) {
    return current === 0 ? 0 : 100 * current; // Return 400% if current is 4 and previous is 0
  }
  return ((current - previous) / previous) * 100;
}

export const returnShipmentDataWithActions = async (req, res) => {
  const { id } = req.params;
  try {
    const shipments = await Shipment.aggregate([
      {
        $match: {
          _id: new mongoose.Types.ObjectId(id),
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          attributes: 1,
          timeline: 1,
          distributor_data: 1,
          lineItems: {
            $map: {
              input: "$lineItems",
              as: "item",
              in: {
                sku: "$$item.sku",
                image: "$$item.image",
                productTitle: "$$item.productTitle",
                variantTitle: "$$item.variantTitle",
                requested: "$$item.requested",
                price: "$$item.price",
              },
            },
          },
          order: 1,
          status: 1,
        },
      },
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "statusData",
        },
      },
      {
        $unwind: {
          path: "$statusData",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          attributes: 1,
          order: 1,
          timeline: 1,
          distributor_data: 1,
          lineItems: 1,
          "status.colorCode": "$statusData.colorCode",
          "status.status": "$statusData.status",
          "status._id": "$statusData._id",
          "status.pseudoId": "$statusData.pseudoId",
        },
      },
    ]);

    if (shipments.length === 0) {
      res.send({ error: "No shipment found with given id." });
      return;
    }

    const orderData = await Order.aggregate([
      {
        $match: {
          _id: shipments[0].order,
        },
      },

      {
        $lookup: {
          from: "addresses",
          foreignField: "_id",
          localField: "shipping_address",
          as: "shipping_address",
        },
      },

      {
        $unwind: {
          path: "$shipping_address",
        },
      },

      {
        $project: {
          _id: 1,
          "shipping_address.address1": 1,
          "shipping_address.address2": 1,
          "shipping_address.city": 1,
          "shipping_address.country": 1,
          createdAt: 1,
        },
      },
    ]);

    if (orderData.length === 0) {
      res.send({ error: "No order found with given shipment." });
      return;
    }

    const updatedShipment = { ...shipments[0], order: orderData[0] };

    const current_status = updatedShipment.status._id;

    const actions = await StatusActionLink.aggregate([
      {
        $match: {
          current_status_id: new ObjectId(current_status),
          // enabled: true,
        },
      },
      {
        $lookup: {
          from: "actions",
          localField: "action_id",
          foreignField: "_id",
          as: "action",
        },
      },
      {
        $unwind: "$action",
      },
      {
        $project: {
          action_display_name: 1,
          formData: 1,
          actionRoute: "$action.route",
          action_unique_key: "$action.action_unique_key",
        },
      },
    ]);

    res.send({ shipment: updatedShipment, actions });
  } catch (error) {
    console.log("returnShipmentDataWithActions", error);
    res.send({ error: "Something went wrong" });
  }
};

export const downloadShipmentExcel = async (req, res) => {
  const shipmentId = req.query.shipment_id;
  const shipment = await Shipment.findOne({ _id: shipmentId });
  const status = await Status.findById(shipment.status);

  let formattedStatus = "";
  if (status) {
    formattedStatus = status.status.includes(" ")
      ? status.status.replace(/\s+/g, "-")
      : status.status;
  }

  if (status.pseudoId !== "ALIGNMENT_PENDING") {
    let sheetPayload = [];
    let sheetHeader = [
      "ProductTitle",
      "IBDSKU",
      "SAPSKU",
      "AllocatedInventory",
      "ChangeRequest",
    ];
    sheetPayload.push(sheetHeader);

    // Generate payload based on sapSkusUsed
    shipment.lineItems.forEach((lineItem) => {
      lineItem.sapSkusUsed.forEach((sapSku) => {
        sheetPayload.push([
          lineItem.productTitle, // Product title
          lineItem.sku,
          sapSku.sku, // SKU from sapSkusUsed
          sapSku.quantityTaken || 0, // Requested quantity from lineItem
          sapSku.quantityTaken || 0, // Quantity taken from sapSkusUsed
        ]);
      });
    });

    // Create the workbook and worksheet
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);
    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

    const fileName = `${formattedStatus}_${shipment.name}.xlsx`;

    // Write the workbook to a file
    xlsx.writeFile(workbook, fileName);

    // Send the file to the client
    res.setHeader("Content-disposition", `attachment; filename=${fileName}`);
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    const fileStream = fs.createReadStream(fileName);
    fileStream.pipe(res);

    // Delete the file after the stream ends
    fileStream.on("close", () => {
      fs.unlinkSync(fileName);
    });
  } else {
    let sheetPayload = [];
    let sheetHeader = [
      "SHIPMENT_ID",
      "SKU",
      "Price",
      "Quantity",
      "Distributor",
      "Image",
    ];
    sheetPayload.push(sheetHeader);

    // Generate payload based on sapSkusUsed
    shipment.lineItems.forEach((lineItem) => {
      sheetPayload.push([
        shipment.name,
        lineItem.sku,
        lineItem.price,
        lineItem.requested,
        shipment.distributor_data.name,
        lineItem.image,
      ]);
    });

    // Create the workbook and worksheet
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);
    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

    const fileName = `${formattedStatus}_${shipment.name}.xlsx`;

    // Write the workbook to a file
    xlsx.writeFile(workbook, fileName);

    // Send the file to the client
    res.setHeader("Content-disposition", `attachment; filename=${fileName}`);
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    const fileStream = fs.createReadStream(fileName);
    fileStream.pipe(res);

    // Delete the file after the stream ends
    fileStream.on("close", () => {
      fs.unlinkSync(fileName);
    });
  }
};

/* 
 Implemenatation: 
 - getCustmerData function will be used to fetch the customer
 - metafields from the shopify.
 @ params (string) customerId: Global shopify customer Id
 @ return (object): It will return the object of the metafield of single line
   text.

*/

export const getTheCustomerData = async (customerId) => {
  const data = customerQueries.getCustomer(customerId);

  const config = {
    method: "POST",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(data),
  };

  const response = await axios(config);

  if (!response?.data?.data?.customer) {
    return {
      success: false,
      errors: "Customer Does not Exist.",
    };
  }
  if (response?.data?.errors?.length > 0) {
    const errors = response.data.errors.map((err) => err.message).join(",");

    console.log(" Error in getting the distributer details", errors);

    return {
      success: false,
      errors: errors,
    };
  }

  const {
    payment_terms,
    source,
    inco_terms,
    shipping_instruction,
    delivery,
    partshipment,
    port_of_loading,
    port_of_discharge,
  } = response.data.data.customer;

  const formattedDistributorDetails = {
    payment_terms: payment_terms?.value ?? "",
    source: source?.value ?? "",
    inco_terms: inco_terms?.value ?? "",
    shipping_instruction: shipping_instruction?.value ?? "",
    delivery: delivery?.value ?? "",
    partshipment: partshipment?.value ?? "",
    port_of_loading: port_of_loading?.value ?? "",
    port_of_discharge: port_of_discharge?.value ?? "",
  };

  return { success: true, data: formattedDistributorDetails };
};

export const generateShipmentPISheet = async (req, res, next) => {
  try {
    const shipmentId = req.query.shipment_id;
    if (!shipmentId || shipmentId === "undefined") {
      return next(new AppError("Missing shipment Id", 400));
    }

    const shipment = await Shipment.findOne({ _id: shipmentId });

    if (!shipment) {
      return next(new AppError(`No Shipment found with ${shipmentId}`, 400));
    }

    const totalFulfilled = shipment.lineItems.reduce(
      (sum, item) => sum + item.fulfilled,
      0
    );

    const existingPISheet = await PISheet.findOne({
      shipment_name: shipment.name,
    });

    const distributorId = shipment.order.distributor._id;
    const distributorDetail = await Distributor.findOne({ _id: distributorId });

    // let sheetPayload = [];

    const sheetHeader = [
      "Date",
      "Material",
      "IBD SKU",
      "Brand",
      "Type",
      "Cluster",
      "Description",
      "EAN No.",
      "Qty",
      "FOB",
      "Value",
    ];

    // sheetPayload.push(sheetHeader);

    let totalQuantity = 0;
    let totalPrice = 0;

    const totalLineItemsSkus = shipment.lineItems.map((item) => item.sku);

    const existingInventoryMap = new Map();

    //find from the inventory
    const lineItemsFromInventory = await Inventory.find(
      {
        sku: { $in: totalLineItemsSkus },
      },
      {
        sku: 1,
        productTitle: 1,
        hsnCode: 1,
        sapSkuUsed: 1,
        price: 1,
        brand: 1,
        cluster: 1,
        gender: 1,
        collection: 1,
      }
    );

    lineItemsFromInventory.forEach((item) => {
      if (!existingInventoryMap.has(item.sku)) {
        existingInventoryMap.set(item.sku, item);
      }
    });

    const sheetPayload = shipment.lineItems.flatMap((lineItem, index) =>
      lineItem.sapSkusUsed.map((skuData) => {
        const { sku, quantityTaken, eanNo } = skuData;

        // Use await here as the function is now asynchronous
        // const data = await Inventory.findOne({ sku: lineItem.sku });

        const data = existingInventoryMap.get(lineItem.sku);
        const quantity = quantityTaken || 0;
        const price = lineItem.price || 0;
        const value = (price * quantity).toFixed(2);

        totalQuantity += quantity;
        totalPrice += parseFloat(value);

        return [
          shipment.createdAt,
          sku, // Repeating for IBD SKU
          lineItem.sku, // Use sapSkusUsed SKU
          data.brand,
          data.gender,
          data.cluster,
          `${lineItem.sku} - ${data.cluster} ${
            data.collection ? "-" + data.collection : ""
          }`,
          eanNo,
          quantity, // Quantity taken
          price, // Price (FOB)
          value, // Calculated value
        ];
      })
    );

    const updatedSheetPayload = [sheetHeader, ...sheetPayload];

    // Adding Total Row
    updatedSheetPayload.push([
      "",
      "",
      "",
      "",
      "",
      "",
      "Total",
      "",
      totalQuantity,
      "",
      totalPrice.toFixed(2),
    ]);

    // Create Excel Sheet
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(updatedSheetPayload);
    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

    const date = new Date();

    const latestPISheet = await PISheet.findOne(
      {},
      {},
      { sort: { createdAt: -1 } }
    );

    let newSerialNumber = "0001"; // Default if no records exist
    if (latestPISheet) {
      const lastSNo = parseInt(latestPISheet.sNo, 10); // Convert to number
      newSerialNumber = String(lastSNo + 1).padStart(4, "0"); // Increment & format
    }

    let fileName = `PI-${newSerialNumber}-${
      distributorDetail?.country?.substring(0, 3).toUpperCase() || "IND"
    }-${month[date.getMonth()]}${date
      .getFullYear()
      .toString()
      .substr(-2)}.xlsx`;
    let name = `PI-${newSerialNumber}-${
      distributorDetail?.country?.substring(0, 3).toUpperCase() || "IND"
    }-${month[date.getMonth()]}${date.getFullYear().toString().substr(-2)}`;

    if (existingPISheet) {
      newSerialNumber = existingPISheet.sNo;
      fileName = `${existingPISheet?.name}.xlsx`;
      name = existingPISheet.name;
    }

    const piSheetPath = path.join(
      __dirname,
      "..",
      "asset",
      "pi",
      `${fileName}`
    );

    xlsx.writeFile(workbook, piSheetPath);

    //! GENERATE PDF (unchanged from your implementation)

    const pdfFileName = fileName.replace(".xlsx", ".pdf");
    const pdfFilePath = path.join(
      __dirname,
      "..",
      "asset",
      "pi",
      `${pdfFileName}`
    );

    // finding the inventories

    // generating the hsn codes

    const unique_HSN_CODES = [
      ...new Set(
        lineItemsFromInventory.map((item) => item.hsnCode).filter(Boolean)
      ),
    ];

    const result = await getTheCustomerData(
      `gid://shopify/Customer/${distributorDetail.shopifyCustomerId}`
    );

    if (!result.success) {
      throw new Error(result.errors);
    }

    let invoiceDetails;

    try {
      const items = shipment.lineItems.flatMap((lineItem, index) =>
        lineItem.sapSkusUsed.map((sapSku) => ({
          item: index + 1,
          ibd_sku: lineItem.productTitle,
          sap_sku: sapSku.sku,
          quantity: sapSku.quantityTaken || 0, // Use quantityTaken from sapSkusUsed
          price: lineItem.price,
          amountSum: lineItem.price * (sapSku.quantityTaken || 0), // Calculate amount sum
        }))
      );

      const extraChargeValue = shipment?.attributes
        ?.flatMap((x) => x?.value || [])
        ?.reverse()
        ?.find((y) => y.input_id === "663c4c92864c679bd21974c6")?.value;

      const subtotal = items.reduce((sum, item) => sum + item.amountSum, 0);

      const totalQuantity = items.reduce((acc, item) => acc + item.quantity, 0);

      invoiceDetails = {
        items,
        invoiceNumber: name.split("-").join("/"),
        paid: 0, // Update based on actual paid value
        shipping_address: shipment.order.shipping_address,
        distributorAddress: result.data,
        subtotal,
        totalQuantity,
        client: {
          ...shipment.order.distributor,
          ...shipment.order.shipping_address,
        },
        extraChargeValue: extraChargeValue || 0,
        hsnCodes: unique_HSN_CODES,
      };

      generateInvoicePdf(invoiceDetails, pdfFilePath);
    } catch (error) {
      console.error("Error generating PDF:", error);
    }

    // // Upload Files to S3
    const uploadResponse = await uploadToS3(
      piSheetPath,
      process.env.AWS_S3_BUCKET,
      fileName
    );

    const uploadResponsePdf = await uploadToS3(
      pdfFilePath,
      process.env.AWS_S3_BUCKET,
      pdfFileName
    );

    const normalizedExcelPath = path.normalize(piSheetPath);
    const normalizedPdfPath = path.normalize(pdfFilePath);

    fs.unlinkSync(normalizedExcelPath, (err) => {
      if (err) {
        console.log("Error in deteting the excel file", err);
      }
    });
    fs.unlinkSync(normalizedPdfPath, (err) => {
      if (err) {
        console.log("Error in deleting the pdf file", err);
      }
    });

    await PISheet.findOneAndUpdate(
      { shipment_name: shipment.name },
      {
        $set: {
          totalItems: totalFulfilled,
          name: name,
          excelSheetUrl: uploadResponse.Location,
          pdfSheetUrl: uploadResponsePdf.Location,
        },
        $setOnInsert: {
          sNo: newSerialNumber,
        },
      },
      { upsert: true, new: true }
    );

    // Update Shipment with file links
    await Shipment.updateOne(
      { _id: shipment._id },
      {
        piSheet: uploadResponse.Location,
        piPdf: uploadResponsePdf.Location,
        piName: newSerialNumber,
      }
    );

    // Email Notification (unchanged)

    const sheetDownloadUrl = await generateSignedURL(
      uploadResponse.Bucket,
      uploadResponse.Key
    );

    const pdfDownloadUrl = await generateSignedURL(
      uploadResponsePdf.Bucket,
      uploadResponsePdf.Key
    );

    res.status(200).send({
      data: {
        status: "success",
        sheetDownloadUrl: sheetDownloadUrl,
        pdfDownloadUrl: pdfDownloadUrl,
      },
    });
  } catch (error) {
    console.log(error);
    res.status(500).send(error);
  }
};

export const editShipment = async (req, res, next) => {
  try {
    const shipmentId = req.params.shipmentId;
    const { action_link_id, shipment_id } = req.query;
    const { form_data, current_user } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }

    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();
    const current_user_deptt = current_user.departmentType.map((dept) =>
      dept._id.toString()
    );

    if (!current_user_deptt.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    if (!req.file) return next(new AppError("No file attached"));
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);

    // Convert the first sheet to JSON
    const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    const existingShipment = await Shipment.findById(shipmentId);

    const comparison = compareItems(jsonData, existingShipment.lineItems);

    if (comparison.error) {
      return res.send({ error: comparison.error });
    }

    const allShipments = await Shipment.find({
      order: existingShipment.order._id,
    });

    const status = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });

    const length = allShipments.length;

    if (comparison.remainingItemsShipment.length) {
      const nonAligned = {
        name: existingShipment.order.name + `_${length + 1}`,
        order: existingShipment.order._id,
        order_data: {
          _id: existingShipment.order._id,
          name: existingShipment.order.name,
        },
        distributor_data: {
          name: existingShipment.distributor_data.name,
          _id: existingShipment.distributor_data._id.toString(),
          email: existingShipment.distributor_data.email,
          country: existingShipment.distributor_data.country,
        },
        status: "663c4ac9864c679bd21974a8", //this need to be dynamic
        amount: comparison.remainingItemsShipment.reduce((acc, lineItem) => {
          return acc + lineItem.requested * lineItem.price;
        }, 0),
        shipping_address: existingShipment.shipping_address,
        lineItems: comparison.remainingItemsShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
        status_change_history: [
          {
            status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
          },
        ],
        initial_status: "non-aligned",
      };
      await new Shipment(nonAligned).save();
      const updateorder = await Order.findByIdAndUpdate(
        existingShipment.order._id,
        {
          $push: {
            timeline: {
              time: new Date(),
              comment: `Non-Aligned shipment:${nonAligned.name} created from Shipment:${existingShipment.name}`,
            },
            status_change_history: {
              status: "663c4ac9864c679bd21974a8", //this need to be dynamic
            },
          },
        }
      );
    }

    //release inventory
    if (comparison.inventoryToRelease.length) {
      const bulkUpdateOps = comparison.inventoryToRelease.map((x) => ({
        updateOne: {
          filter: { "sapSkus.sku": x.sku },
          update: {
            $inc: {
              "sapSkus.$[element].quantity": x.quantity, // Update the quantityTaken of the matching sapSku
            },
            $set: {
              "sapSkus.$[element].onHold": x.on_hold, // Update the on_hold value of the matching sapSku
            },
          },
          arrayFilters: [
            { "element.sku": x.sku }, // Ensure the update targets the correct sapSku
          ],
        },
      }));

      try {
        const bulkWriteResult = await Inventory.bulkWrite(bulkUpdateOps);
      } catch (error) {
        console.error("Error during bulkWrite:", error);
      }
    }

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    const updateShipment = await Shipment.findByIdAndUpdate(shipmentId, {
      $set: {
        lineItems: comparison.updatedShipment.filter((x) => x.requested),
        status: statusActionLinkData.next_status_id,
        status_data: {
          name: status.status,
        },
      },
      $push: {
        timeline: {
          time: new Date(),
          comment: `Shipment has been edited by ${current_user.name}`,
        },
        status_change_history: {
          status: statusActionLinkData.next_status_id,
        },
      },
    });

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });
    const departmentPeoples = await Department.find({
      departmentType: { $in: [nextStatus.departmentType._id] },
    });

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    const departmentNotified = await getDepartmentNotifiedEmails(nextStatus);
    const company = await Company.findOne({
      shopifyCompanyId: distributor.shopifyCompanyId,
    });

    //Note needed anymore
    // let subject = nextStatus.status;

    // const recipients = departmentPeoples.map((departmentPeople) => ({
    //   name: departmentPeople.name,
    //   email: departmentPeople.email,
    //   cc: [...departmentNotified],
    // }));
    // if (recipients.length > 0) {
    //   await EmailNotification.create({
    //     emailCategory: "SHIPMENT",
    //     emailType: "SHIPMENT_STATUS_CHANGE",
    //     reciepient: recipients,
    //     emailPayload: {
    //       orderName: shipment.order.name,
    //       distributorName: distributor.name,
    //       date: shipment.order.createdAt,
    //       shipmentName: shipment.name,
    //       shipmentStatus: nextStatus.status,
    //       shipmetRef: shipment.ref,
    //       currentStatus: currentStatus.status,
    //       nextStatus: nextStatus.status,
    //       actionByUser: current_user.name,
    //       piName: shipment?.piName,
    //       company: company.name,
    //       subject: subject,
    //     },
    //   });
    // }

    //*delete file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error("Error deleting file:", err);
      } else {
        console.log("File deleted successfully");
      }
    });

    res.status(201).json(updateShipment);
  } catch (error) {
    console.log("editShipmentError", error);
    res.send({ error: "Something went wrong" });
  }
};

function compareItems(requestedItems, existingItems) {
  const existingItemsMap = new Map();

  // Map existing items by SKU for quick lookup
  existingItems.forEach((item) => {
    existingItemsMap.set(item.sku, item);
  });

  const updatedShipment = [];
  const remainingItemsShipment = [];
  const inventoryToRelease = [];
  let valid = true;
  let allItemsExist = true;
  let emptyQuantity = false;
  let totalQuantity = 0;

  // Track updated quantities for each SKU to consolidate
  const skuConsolidation = new Map();

  // Track remaining quantities for each SKU
  const remainingQuantities = new Map();

  requestedItems.forEach((reqItem) => {
    if (valid && allItemsExist) {
      // Find the corresponding existing item by SKU
      const existingItem = existingItemsMap.get(reqItem.IBDSKU);

      if (!existingItem) {
        allItemsExist = false;
        return;
      }

      // Check if quantity is valid
      if (!(reqItem.ChangeRequest || reqItem.ChangeRequest === 0)) {
        emptyQuantity = true;
        return;
      }

      totalQuantity += reqItem.ChangeRequest;

      // Process SAP SKU for the requested item
      const sapSkuUsed = existingItem.sapSkusUsed.find(
        (sapSku) => sapSku.sku === reqItem.SAPSKU
      );
      if (!sapSkuUsed) {
        allItemsExist = false;
        return;
      }

      const decrement = sapSkuUsed.quantityTaken - reqItem.ChangeRequest;

      // Check if the requested quantity is valid
      if (decrement < 0) {
        valid = false;
      } else {
        // Consolidate the requested items by SKU
        const existingConsolidatedItem = skuConsolidation.get(
          reqItem.IBDSKU
        ) || {
          ...existingItem,
          sapSkusUsed: [...existingItem.sapSkusUsed],
          fulfilled: 0, // Reset to avoid carrying previous data
          requested: 0, // Reset to avoid carrying previous data
        };

        // Update the existing item based on the change request
        existingConsolidatedItem.fulfilled += reqItem.ChangeRequest;
        existingConsolidatedItem.requested += reqItem.ChangeRequest;

        existingConsolidatedItem.sapSkusUsed =
          existingConsolidatedItem.sapSkusUsed.map((sku) =>
            sku.sku === reqItem.SAPSKU
              ? { ...sku, quantityTaken: reqItem.ChangeRequest }
              : sku
          );

        skuConsolidation.set(reqItem.IBDSKU, existingConsolidatedItem);

        // Update inventory to release
        inventoryToRelease.push({
          sku: reqItem.SAPSKU,
          quantity: decrement,
          on_hold: reqItem.ChangeRequest,
        });

        // Track remaining quantity for this SKU
        const currentRemaining =
          remainingQuantities.get(reqItem.IBDSKU) || existingItem.requested;
        remainingQuantities.set(
          reqItem.IBDSKU,
          currentRemaining - reqItem.ChangeRequest
        );
      }
    }
  });

  // Build the final updatedShipment and remainingItemsShipment from the consolidated data
  skuConsolidation.forEach((item) => {
    updatedShipment.push(item);

    // Calculate remaining quantity
    const remainingQuantity = remainingQuantities.get(item.sku) || 0;
    if (remainingQuantity > 0) {
      remainingItemsShipment.push({
        ...item,
        requested: remainingQuantity,
        fulfilled: 0,
        sapSkusUsed: [],
      });
    }
  });

  // Check for errors
  if (totalQuantity === 0) {
    return { error: "Enter valid Quantity, at least 1 SKU has quantity > 0" };
  }

  if (emptyQuantity) {
    return { error: "All Quantity boxes should be filled, value can be 0" };
  }

  if (!valid) {
    return { error: "Quantity can't be greater than previously allocated" };
  }

  if (!allItemsExist) {
    return { error: "Uploaded items must be the same as downloaded" };
  }

  return {
    updatedShipment,
    remainingItemsShipment,
    inventoryToRelease,
  };
}

export const editShipmentWithSla = async (req, res, next) => {
  try {
    const shipmentId = req.params.shipmentId;
    const { action_link_id, shipment_id, sla } = req.query;
    const { form_data, current_user } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }

    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();
    const current_user_deptt = current_user.departmentType.map((dept) =>
      dept._id.toString()
    );

    if (!current_user_deptt.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    if (!req.file) return next(new AppError("No file attached"));
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);

    // Convert the first sheet to JSON
    const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    const existingShipment = await Shipment.findById(shipmentId);

    const comparison = compareItems(jsonData, existingShipment.lineItems);

    if (comparison.error) {
      return res.send({ error: comparison.error });
    }

    const allShipments = await Shipment.find({
      order: existingShipment.order._id,
    });

    let length = allShipments.length;

    const valuesToPushInOrder = [];

    const groupedBySla = comparison.updatedShipment
      .filter((x) => x.requested)
      .reduce((acc, curr) => {
        const index = acc.findIndex((item) => item[0]?.sla === curr.sla);
        if (index !== -1) {
          acc[index].push(curr);
        } else {
          acc.push([curr]);
        }

        return acc;
      }, []);

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    for (let i = 0; i < groupedBySla.length; i++) {
      const subArray = groupedBySla[i];
      if (i === 0) {
        const updateShipment = await Shipment.findByIdAndUpdate(shipmentId, {
          $set: {
            lineItems: subArray,
            status: statusActionLinkData.next_status_id,
            sla: {
              days: sla ? Number(sla) : 0,
            },
          },
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment has been edited by ${current_user.name}`,
            },
            status_change_history: {
              status: statusActionLinkData.next_status_id,
            },
          },
        });
      } else {
        const remaining = {
          name: existingShipment.order.name + `_${length + 1}`,
          order: existingShipment.order._id,
          status: statusActionLinkData.next_status_id, //this need to be dynamic
          amount: 5000,
          shipping_address: existingShipment.shipping_address,
          lineItems: comparison.remainingItemsShipment,
          timeline: [
            {
              time: new Date(),
              comment: `Awaiting with SLA shipment created from Shipment:${existingShipment.name}`,
            },
          ],
          status_change_history: [
            {
              status: statusActionLinkData.next_status_id,
            },
          ],
        };
        await new Shipment(remaining).save();
        length++;
        valuesToPushInOrder.push({
          time: new Date(),
          comment: `Shipment ${remaining.name} created with SLA.`,
        });
      }
    }

    if (comparison.remainingItemsShipment.length) {
      const nonAligned = {
        name: existingShipment.order.name + `_${length + 1}`,
        order: existingShipment.order._id,
        status: "663c4ac9864c679bd21974a8", //this need to be dynamic
        amount: 5000,
        shipping_address: existingShipment.shipping_address,
        lineItems: comparison.remainingItemsShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
        status_change_history: [
          {
            status: "663c4ac9864c679bd21974a8", //this need to be dynamic
          },
        ],
      };
      await new Shipment(nonAligned).save();
    }

    const updateorder = await Order.findByIdAndUpdate(
      existingShipment.order._id,
      {
        $set: {
          status: "664372f129f6db844ee8bc0b", //this need to be dynamic
        },
        $push: { timeline: { $each: valuesToPushInOrder } },
      }
    );

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });
    const departmentPeoples = await Department.find({
      departmentType: { $in: [nextStatus.departmentType._id] },
    });

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    const departmentNotified = await getDepartmentNotifiedEmails(nextStatus);
    const company = await Company.findOne({
      shopifyCompanyId: distributor.shopifyCompanyId,
    });

    //Not needed anymore

    // let subject = nextStatus.status;

    // const recipients = departmentPeoples.map((departmentPeople) => ({
    //   name: departmentPeople.name,
    //   email: departmentPeople.email,
    //   cc: [...departmentNotified],
    // }));

    // if (recipients.length > 0) {
    //   await EmailNotification.create({
    //     emailCategory: "SHIPMENT",
    //     emailType: "SHIPMENT_STATUS_CHANGE",
    //     reciepient: recipients,
    //     emailPayload: {
    //       orderName: shipment.order.name,
    //       distributorName: distributor.name,
    //       date: shipment.order.createdAt,
    //       shipmentName: shipment.name,
    //       shipmentStatus: nextStatus.status,
    //       shipmetRef: shipment.ref,
    //       currentStatus: currentStatus.status,
    //       nextStatus: nextStatus.status,
    //       actionByUser: current_user.name,
    //       piName: shipment?.piName,
    //       company: company.name,
    //       subject: subject,
    //     },
    //   });
    // }

    //*delete file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error("Error deleting file:", err);
      } else {
        console.log("File deleted successfully");
      }
    });

    res.status(201).json({ success: true });
  } catch (error) {
    console.log("editShipmentError", error);
    res.send({ error: "Something went wrong" });
  }
};

// calculate the shipments aligned
export const shipmentsAligned = async (req, res) => {
  try {
    const currentDate = new Date();
    const pastDate = new Date();
    pastDate.setDate(currentDate.getDate() - 30);

    const previousMonth = new Date(pastDate);
    previousMonth.setDate(pastDate.getDate() - 30);

    const orderIds = await Order.distinct("_id", {
      createdAt: {
        $gte: pastDate,
        $lte: currentDate,
      },
    });

    const pastOrderIds = await Order.distinct("_id", {
      createdAt: {
        $gte: previousMonth,
        $lte: pastDate,
      },
    });

    const allShipmentsGrouped = await Shipment.aggregate([
      {
        $match: {
          $or: [{ order: { $in: pastOrderIds } }, { order: { $in: orderIds } }],
        },
      },
      {
        $project: {
          _id: 1,
          status: 1,
          order: 1,
          initial_status: 1,
          final_status: 1,
        },
      },
      {
        $lookup: {
          from: "orders",
          localField: "order",
          foreignField: "_id",
          as: "order",
        },
      },
      {
        $unwind: {
          path: "$order",
        },
      },
      {
        $project: {
          _id: 1,
          status: 1,
          initial_status: 1,
          final_status: 1,
          order: {
            _id: "$order._id",
            name: "$order.name",
          },
        },
      },
      {
        $group: {
          _id: null,
          pastShipments: {
            $push: {
              $cond: {
                if: { $in: ["$order._id", pastOrderIds] },
                then: "$$ROOT",
                else: "$$REMOVE",
              },
            },
          },
          currentShipments: {
            $push: {
              $cond: {
                if: { $in: ["$order._id", orderIds] },
                then: "$$ROOT",
                else: "$$REMOVE",
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          pastShipments: 1,
          currentShipments: 1,
        },
      },
    ]);

    const pastShipments = allShipmentsGrouped[0]?.pastShipments || [];
    const shipments = allShipmentsGrouped[0]?.currentShipments || [];

    const calculateShipments = calculateShipmentStats(shipments);
    const calculatePreviousShipments = calculateShipmentStats(pastShipments);

    const totalShipments = Object.values(calculateShipments).reduce(
      (a, b) => a + b,
      0
    );
    const totalPreviousShipments = Object.values(
      calculatePreviousShipments
    ).reduce((a, b) => a + b, 0);

    const statusArray = Object.keys(calculateShipments).map((status) => {
      const currentCount = calculateShipments[status] || 0;
      const previousCount = calculatePreviousShipments[status] || 0;
      const currentPercentage = (currentCount / totalShipments) * 100;
      const previousPercentage = (previousCount / totalPreviousShipments) * 100;
      const percentageChange =
        ((currentCount - previousCount) / (previousCount || 1)) * 100;
      const changeType = percentageChange > 0 ? "increased" : "decreased";

      return {
        status,
        currentCount,
        previousCount,
        currentPercentage,
        previousPercentage,
        percentageChange,
        totalPreviousShipments,
        totalShipments,
        changeType,
      };
    });

    res.send(statusArray);
  } catch (error) {
    console.error(error);
    res.status(500).send("Something went wrong.");
  }
};

export function calculateShipmentStats(shipments) {
  const shipmentsByOrder = shipments.reduce((acc, shipment) => {
    const orderId = shipment.order._id.toString();
    if (!acc[orderId]) {
      acc[orderId] = [];
    }
    acc[orderId].push(shipment);
    return acc;
  }, {});

  const processedShipments = [];
  for (const orderId in shipmentsByOrder) {
    const orderShipments = shipmentsByOrder[orderId];
    const initialStatuses = new Set(
      orderShipments.map((shipment) => {
        if (shipment.initial_status != undefined)
          return shipment.initial_status;
      })
    );
    initialStatuses.delete(undefined);

    const isPartialAligned = initialStatuses.size > 1;

    orderShipments.forEach((shipment) => {
      const finalStatus = isPartialAligned
        ? "partial-aligned"
        : shipment.initial_status;
      processedShipments.push({ ...shipment, final_status: finalStatus });
    });
  }

  const statusCounts = processedShipments.reduce(
    (acc, shipment) => {
      const orderName = shipment.order.name;
      const status = shipment.final_status;

      if (!acc.uniqueOrders[orderName]) {
        acc.uniqueOrders[orderName] = true;
        if (!acc.statusCounts[status]) {
          acc.statusCounts[status] = 0;
        }
        acc.statusCounts[status]++;
      }

      return acc;
    },
    { uniqueOrders: {}, statusCounts: {} }
  ).statusCounts;

  // Ensure all expected statuses are present in the result, even if they are zero
  const expectedStatuses = ["aligned", "non-aligned", "partial-aligned"];
  expectedStatuses.forEach((status) => {
    if (!statusCounts[status]) {
      statusCounts[status] = 0;
    }
  });

  return statusCounts;
}

// export const shipmentsSummary = async (req, res) => {
//   try {
//     const currentDate = new Date();
//     const pastDate = new Date();
//     pastDate.setDate(currentDate.getDate() - 30);

//     const previousMonth = new Date(pastDate);
//     previousMonth.setDate(pastDate.getDate() - 30);

//     const [shipments, pastShipments] = await Promise.all([
//       Shipment.find({
//         createdAt: {
//           $gte: pastDate,
//           $lte: currentDate
//         }
//       }),
//       Shipment.find({
//         createdAt: {
//           $gte: previousMonth,
//           $lte: pastDate,
//         }
//       })
//     ]);

//     const calculateCounts = (shipments) => {
//       let nonAlignedCount = 0;
//       let fulfilledCount = 0;
//       let nonFulfilledCount = 0;

//       shipments.forEach((value) => {
//         if (value.initial_status === "non-aligned") nonAlignedCount++;
//         if (value.status.flowType === "happy") fulfilledCount++;
//         if (value.status.flowType === "unhappy") nonFulfilledCount++;
//       });

//       return { nonAlignedCount, fulfilledCount, nonFulfilledCount };
//     };

//     const currentCounts = calculateCounts(shipments);
//     const pastCounts = calculateCounts(pastShipments);

//     const createShipmentStats = (currentCount, pastCount, totalCurrent, totalPast) => ({
//       count: currentCount,
//       previousCount: pastCount,
//       totalCount: currentCount + pastCount,
//       changeType: (((currentCount - pastCount) / (pastCount || 1)) * 100) > 0 ? 'increased' : 'decreased',
//       percentageChange: ((currentCount - pastCount) / (pastCount || 1)) * 100,
//       currentPercentage: (currentCount / totalCurrent) * 100,
//       previousPercentage: (pastCount / totalPast) * 100
//     });

//     const totalShipments = shipments.length;
//     const totalPreviousShipments = pastShipments.length;

//     const result = [
//       {
//         type: 'Total Non-Aligned Shipments',
//         ...createShipmentStats(currentCounts.nonAlignedCount, pastCounts.nonAlignedCount, totalShipments, totalPreviousShipments)
//       },
//       {
//         type: 'Fulfilled using Domestic Divergence',
//         ...createShipmentStats(currentCounts.fulfilledCount, pastCounts.fulfilledCount, totalShipments, totalPreviousShipments)
//       },
//       {
//         type: 'Pending',
//         ...createShipmentStats(currentCounts.nonFulfilledCount, pastCounts.nonFulfilledCount, totalShipments, totalPreviousShipments)
//       }
//     ];

//     res.send(result);

//   } catch (err) {
//     res.send("Something went wrong.");
//   }

// };

export const shipmentsSummary = async (req, res) => {
  try {
    const now = new Date();
    const last30DaysEnd = new Date(now); // Today
    const last30DaysStart = new Date(now);
    last30DaysStart.setDate(now.getDate() - 30);

    const previous30DaysEnd = new Date(last30DaysStart);
    const previous30DaysStart = new Date(last30DaysStart);
    previous30DaysStart.setDate(last30DaysStart.getDate() - 30);

    const shipmentsLast30Days = await Shipment.countDocuments({
      initial_status: "non-aligned",
      createdAt: { $gte: last30DaysStart, $lt: last30DaysEnd },
    });

    const shipmentsPrev30Days = await Shipment.countDocuments({
      initial_status: "non-aligned",
      createdAt: { $gte: previous30DaysStart, $lt: previous30DaysEnd },
    });

    let shipmentPendingLast30Days = await Shipment.aggregate([
      { $match: { createdAt: { $gte: last30DaysStart, $lt: last30DaysEnd } } },
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      { $match: { "status.pseudoId": "ALIGNMENT_PENDING" } },
      { $count: "shipmentPendingLast30DaysCount" },
    ]);

    shipmentPendingLast30Days =
      shipmentPendingLast30Days.length > 0
        ? shipmentPendingLast30Days[0].shipmentPendingLast30DaysCount
        : 0;

    let shipmentPendingPrev30Days = await Shipment.aggregate([
      {
        $match: {
          createdAt: { $gte: previous30DaysStart, $lt: previous30DaysEnd },
        },
      },
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      { $match: { "status.pseudoId": "ALIGNMENT_PENDING" } },
      { $count: "shipmentPendingPrev30DaysCount" },
    ]);

    shipmentPendingPrev30Days =
      shipmentPendingPrev30Days.length > 0
        ? shipmentPendingPrev30Days[0].shipmentPendingPrev30DaysCount
        : 0;

    const processedShipmentsLast30days =
      shipmentsLast30Days - shipmentPendingLast30Days;
    const processedShipmentsPrev30days =
      shipmentsPrev30Days - shipmentPendingPrev30Days;

    const shipmentPercentage = calculatePercentageChange(
      shipmentsLast30Days,
      shipmentsPrev30Days
    );
    const shipmentPendingPercentage = calculatePercentageChange(
      shipmentPendingLast30Days,
      shipmentPendingPrev30Days
    );
    const shipmentProcessedPercentage = calculatePercentageChange(
      processedShipmentsLast30days,
      processedShipmentsPrev30days
    );

    const shipmentsTrend = shipmentPercentage > 0 ? "increased" : "decreased";
    const shipmentPendingTrend =
      shipmentPendingPercentage > 0 ? "increased" : "decreased";
    const shipmentProcessedTrend =
      shipmentProcessedPercentage > 0 ? "increased" : "decreased";

    const statsArray = [
      {
        heading: "Total Non-Aligned Shipments",
        currentCount: shipmentsLast30Days,
        pastCount: shipmentsPrev30Days,
        changeType: shipmentsTrend,
        percentageChange: shipmentPercentage,
      },
      {
        heading: "Fulfilled using Domestic Divergence",
        currentCount: processedShipmentsLast30days,
        pastCount: processedShipmentsPrev30days,
        changeType: shipmentProcessedTrend,
        percentageChange: shipmentProcessedPercentage,
      },
      {
        heading: "Pending",
        currentCount: shipmentPendingLast30Days,
        pastCount: shipmentPendingPrev30Days,
        changeType: shipmentPendingTrend,
        percentageChange: shipmentPendingPercentage,
      },
    ];

    res.status(200).json({ statsArray });
  } catch (err) {
    res.status(500).json({ error: "Something went wrong." });
  }
};

export const allocateInventoryManually = async (req, res) => {
  try {
    const { action_link_id, shipment_id } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }
    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }
    const result = await alignmentPendingOrderProcessTrigger(
      [shipment.name],
      statusActionLinkData
    );
    let message = ``;
    if (
      !result.createdShipments.length > 0 &&
      !result.updatedShipments.length > 0
    ) {
      message = "No inventory available";
    }
    res.status(200).json({
      error: message,
      data: result,
    });
  } catch (error) {
    console.log("processOrderError", error);
    return res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

/* 
 required fields in the email template
 pi id
 pi Value
 pi Quantity
 invoice Id
 invoice value
 distributor
 pi date

*/

export const sendOrderDispatchedEmail = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    if (!shipmentId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        error: "Shipment Id is missing.",
      });
    }

    const shipment = await Shipment.findOne({
      _id: shipmentId,
    });

    if (!shipment) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        error: "Shipment does not exist.",
      });
    }

    const existingPiSheet = await PISheet.findOne({
      shipment_name: shipment.name,
    });

    if (!existingPiSheet) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        error:
          "Email cannot be send as PI sheet does not exist for the current shipment.",
      });
    }

    const piQuantity = existingPiSheet.totalItems;
    const piDate = existingPiSheet.createdAt;

    const items = shipment.lineItems.flatMap((lineItem, index) =>
      lineItem.sapSkusUsed.map((sapSku) => ({
        item: index + 1,

        amountSum: lineItem.price * (sapSku.quantityTaken || 0),
      }))
    );

    const subtotal = items.reduce((sum, item) => sum + item.amountSum, 0);
    const distributorDetails = await Distributor.findById(
      shipment.distributor_data._id.toString()
    );

    if (!distributorDetails) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        error:
          "Cannot process the email as the distributor details is missing for the current shipment.",
      });
    }

    const shopifyCompanyId = distributorDetails.shopifyCompanyId;

    const company = await Company.findOne({
      shopifyCompanyId,
    });

    if (!company) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        error:
          "Cannot process the email as the company details is missing for the distributor.",
      });
    }

    const companyName = company.name;
    const distributorCountry = shipment.distributor_data?.country || "IND";
    const formattedDate = formattedMonthAndYear(existingPiSheet.createdAt);
    const countryCode = distributorCountry.substring(0, 3).toUpperCase();
    const piId = `PI-${existingPiSheet.sNo}-${countryCode}-${formattedDate}`;
    const piNumber = `PI - ${existingPiSheet.sNo}/${countryCode}/${formattedDate}`;

    const status = shipment.status;

    const notifiedDepartmentPeople = await getDepartmentNotifiedEmails(
      status,
      distributorDetails._id
    );

    const countryManagers = await getCountryManagersOfDistributor(
      distributorDetails._id
    );

    const tranformedLogisticsPeople = await getDepartmentsPeoples(
      "LOGISTICS_AND_PAYMENTS"
    );

    const tranformedSupplyChainPeople = await getDepartmentsPeoples(
      "SUPPLY_CHAIN_OPERATIONS"
    );

    const ccRecipients = await getDepartmentsPeoples("SECONDARY_NOTIFICATION");

    const finalRecipients = onlyUniqueRecipients([
      ...countryManagers,
      ...tranformedLogisticsPeople,
      ...tranformedSupplyChainPeople,
    ]);

    const emailPayload = {
      piId,
      piQuantity,
      piValue: parseFloat(subtotal.toFixed(2)),
      invoiceId: piNumber,
      invoiceValue: parseFloat(subtotal.toFixed(2)),
      distributorName: distributorDetails.name,
      piDate,
      companyName,
    };

    await new Email(
      finalRecipients,
      emailPayload,
      ccRecipients?.map((x) => x.email) || [],
      ""
    ).sendOrderDispachedEmail();

    res.status(200).json({
      responseCode: 0,
      status: "success",
      message: "Order dispatched email sent successfully.",
    });
  } catch (err) {
    console.log(" Error in sending the order dispatched email", err);
    res.status(500).json({
      responseCode: 1,
      status: "failed",
      error:
        "Something went wrong while sending email. Please try again later.",
    });
  }
};
