import express from "express";

import {
  getUsers,
  getOneUser,
  createUser,
  updateUser,
  deleteUser,
  loginUser,
} from "../controller/user.controller..js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";
import decryption from "../service/encryptDecrypt/decryption.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("user", "read"), getUsers)
  .post(authenticateAdminPeopleAccess("user", "write"), createUser);

router.post(
  "/login",
  decryption,
  loginUser
);

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("user", "read"), getOneUser)
  .patch(authenticateAdminPeopleAccess("user", "write"), updateUser)
  .delete(authenticateAdminPeopleAccess("user", "delete"), deleteUser);

export default router;
