import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import Department from "../model/department.model.js";
import { authenticate } from "./departmentAuthenticate.controller.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import catchAsync from "../utils/catchAsync.js";
import Distributor from "../model/distributor.model.js";
import Designation from "../model/designation.model.js";

export const getDepartments = getAll(Department);
export const getOneDepartment = getOne(Department);
export const createDepartment = catchAsync(async (req, res) => {
  const { password, distributor, designation } = req.body;
  let hashedPassword = password;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 10);
  }
  const foundDesignation = await Designation.findById(designation._id);
  const countryManager = req.body._id;

  if (foundDesignation && foundDesignation.isCountryManager) {
    for (const distributorId of distributor) {
      const foundDistributor = await Distributor.findById(distributorId);

      if (foundDistributor) {
        // Push the designation into the countryManager array
        foundDistributor.countryManager.push(countryManager);
        await foundDistributor.save();
      }
    }
  }

  const user = new Department({
    ...req.body,
    password: hashedPassword,
  });

  const savedUser = await user.save();
  delete savedUser.password;
  res.status(201).json(savedUser);
});

export const hashPassword = async (req, res, next) => {
  const { password } = req.body;
  let hashedPassword = password;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 10);
  }
  req.body.password = hashedPassword;
  next();
};

export const updateDepartment = catchAsync(async (req, res) => {
  const doc = await Department.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  const { distributor, designation } = doc;

  if (distributor && designation) {
    const countryManager = req.body._id;
    const foundDesignation = await Designation.findById(designation);

    if (foundDesignation && foundDesignation.isCountryManager) {
      const existingDistributors = await Distributor.find({
        countryManager: { $in: [countryManager] },
      });

      for (const distributorId of distributor) {
        const foundDistributor = await Distributor.findById(distributorId);

        if (foundDistributor) {
          if (!foundDistributor.countryManager.includes(countryManager)) {
            foundDistributor.countryManager.push(countryManager);
            await foundDistributor.save();
          }
        }
      }

      // Step 3: Remove countryManager from distributors that are NOT in the new req.body.distributor list
      for (const existingDist of existingDistributors) {
        if (!distributor.includes(existingDist._id.toString())) {
          // Remove countryManager from distributors no longer in the req.body.distributor list
          existingDist.countryManager = existingDist.countryManager.filter(
            (id) => id.toString() !== countryManager
          );
          await existingDist.save();
        }
      }
    }
  }

  res.status(200).json({
    status: "success",
    result: 1,
    data: {
      data: doc,
    },
  });
});

export const deleteDepartment = deleteOne(Department);

// Controller function for people login
export const loginDepartmentPeople = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const user = await authenticate(email, password);
  if (!user) {
    return res.status(401).json({ error: "Invalid email or password" });
  }

  const token = jwt.sign(
    { userId: user._id, email: user.email, name: user.name, type: user.type },
    process.env.JWT_TOKEN_SECRET,
    { expiresIn: "8h" }
  );

  return res.json({ token, user });
});
