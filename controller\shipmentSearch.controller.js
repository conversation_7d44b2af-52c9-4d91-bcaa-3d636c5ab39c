import Shipment from "../model/shipment.model.js";
import { ObjectId } from "mongodb";

export async function shipmentSearch(req, res) {
  try {
    const {
      input,
      distributor,
      shipmentStatus,
      fromDate,
      toDate,
      flowType,
      page = 1,
      pageSize = 50,
    } = req.body;

    const aggregateQuery = [];
    let hasFilters = false;

    if (
      req.user?.designation?.isCountryManager &&
      Array.isArray(req.user.distributor)
    ) {
      hasFilters = true;
      const distributorIds = req.user.distributor.map((d) => d._id.toString());
      aggregateQuery.push({
        $match: { "distributor_data._id": { $in: distributorIds } },
      });
    }

    if (input && input.trim()) {
      hasFilters = true;
      aggregateQuery.push({
        $match: {
          $or: [
            { name: new RegExp(input, "i") },
            { status: new RegExp(input, "i") },
            { distributor: new RegExp(input, "i") },
            { flowType: new RegExp(input, "i") },
            { createdAt: new RegExp(input, "i") },
          ],
        },
      });
    }

    // Add flowType filter condition
    if (flowType && flowType.trim()) {
      hasFilters = true;
      aggregateQuery.push({
        $match: { flowType: flowType.toLowerCase() },
      });
    }

    // Add date range filter conditions
    if (fromDate && toDate) {
      hasFilters = true;
      aggregateQuery.push({
        $match: {
          createdAt: {
            $gte: new Date(fromDate),
            $lte: new Date(toDate),
          },
        },
      });
    }

    // Add shipmentStatus filter condition
    if (shipmentStatus && shipmentStatus.length > 0) {
      hasFilters = true;
      const shipmentStatusArrayWithObjectId = shipmentStatus.map(
        (value) => new ObjectId(value)
      );
      aggregateQuery.push({
        $match: { status: { $in: shipmentStatusArrayWithObjectId } },
      });
    }

    // Add distributor filter condition
    if (distributor && distributor.length > 0) {
      hasFilters = true;
      aggregateQuery.push({
        $match: { "distributor_data._id": { $in: distributor } },
      });
    }
    // Add lookup stages for status and distributor
    aggregateQuery.push(
      {
        $lookup: {
          from: "status", // Name of the status collection
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      {
        $unwind: { path: "$status" },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          internalRef: 1,
          status: {
            status: 1,
            colorCode: 1,
            pseudoId: 1,
          },
          distributor_data: 1,
          createdAt: 1,
          updatedAt: 1,
        },
      }
    );

    // Pagination

    const limit = pageSize > 50 ? 50 : pageSize;
    const skip = (page - 1) * limit;

    if (hasFilters) {
      // Run the aggregation query with pagination if there are filters
      aggregateQuery.push({ $sort: { createdAt: -1 } });
      aggregateQuery.push({ $skip: skip });
      aggregateQuery.push({ $limit: limit });

      const shipments = await Shipment.aggregate(aggregateQuery).exec();
      const totalCountAggregate = await Shipment.aggregate([
        ...aggregateQuery.slice(0, -2), // Exclude pagination stages for counting
        { $count: "total" },
      ]).exec();
      const total = totalCountAggregate[0]?.total || 0;

      res.send({ shipments, total, page, pageSize });
    } else {
      // Fetch all data with pagination if no filters are provided
      const shipments = await Shipment.aggregate([
        {
          $lookup: {
            from: "status", // Name of the status collection
            localField: "status",
            foreignField: "_id",
            as: "status",
          },
        },
        {
          $unwind: { path: "$status", preserveNullAndEmptyArrays: true },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            internalRef: 1,
            status: {
              status: 1,
              colorCode: 1,
              pseudoId: 1,
            },
            distributor_data: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
      ]).exec();
      const total = await Shipment.countDocuments().exec();
      res.send({ shipments, total, page, pageSize });
    }
  } catch (error) {
    console.log(error);
    res
      .status(500)
      .send({ error: "Something went wrong.", message: error.message });
  }
}
