import mongoose from "mongoose";

const departmentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    shopifyCompanyId: {
      type: String,
    },
    email: {
      type: String,
      required: [true, "User with this email address already exists"],
      unique: true,
    },
    priority: {
      type: String,
    },
    country: {
      type: String,
    },
    internalCode: {
      type: String,
    },
    customerSince: {
      type: String,
    },
    externalId: {
      type: String,
    },
    note: {
      type: String,
    },
    reportingTo: {
      type: String,
    },
    designation: {
      type: mongoose.Schema.ObjectId,
      ref: "Designation",
      required: true,
    },
    distributor: [
      {
        type: mongoose.Schema.ObjectId,
        ref: "Distributor",
        // required: true
      },
    ],
    departmentType: [
      {
        type: mongoose.Schema.ObjectId,
        ref: "DepartmentType",
        required: true,
      },
    ],
    password: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: "deptt_people",
    },
  },
  { timestamps: true }
);

departmentSchema.pre(/^find/, function (next) {
  this.populate({
    path: "distributor",
    // select: "",
  })
    .populate({
      path: "designation",
      // select: "",
    })
    .populate({
      path: "departmentType",
      // select: "",
    });
  next();
});

const Department = mongoose.model("Department", departmentSchema);
export default Department;
