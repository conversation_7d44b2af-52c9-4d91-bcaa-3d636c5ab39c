{"scripts": {"test": "jest", "dev": "nodemon server.js"}, "type": "module", "dependencies": {"@sendgrid/mail": "^8.1.5", "archiver": "^7.0.1", "aws-sdk": "^2.1623.0", "axios": "^1.7.7", "axios-retry": "^4.5.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "express-fileupload": "^1.5.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "fs": "^0.0.1-security", "helmet": "^8.1.0", "html-to-text": "^9.0.5", "jest": "^29.7.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongodb": "^6.5.0", "mongoose": "^8.3.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "nodemon": "^3.1.10", "pdf-lib": "^1.17.1", "pdfkit": "^0.15.0", "pug": "^3.0.2", "sharp": "^0.34.2", "ssh2-sftp-client": "^11.0.0", "to-words": "^4.4.0", "uuid": "^9.0.1", "validator": "^13.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}