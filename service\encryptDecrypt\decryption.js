import crypto from "crypto";

const base64ToBuffer = (base64) => Buffer.from(base64, "base64");
const decryptRSA = (encryptedAESKey) => {
  try {
    const buffer = base64ToBuffer(encryptedAESKey);
    const decryptedKey = crypto.privateDecrypt(
      {
        key: process.env.PRIVATE_KEY,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha256",
      },
      buffer
    );
    return decryptedKey;
  } catch (err) {
    console.log("error in decoding", err);
  }
};

const decryptAES = (encryptedData, aesKey, iv) => {
  const decipher = crypto.createDecipheriv("aes-256-cbc", aesKey, iv);
  let decrypted = decipher.update(encryptedData, "base64", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
};

export default async function decryption(req, res, next) {
  try {
    const { encryptedAESKey, encryptedData, iv } = req.body;
    const aesKey = decryptRSA(encryptedAESKey);
    const decryptedData = decryptAES(
      base64ToBuffer(encryptedData),
      aesKey,
      base64ToBuffer(iv)
    );
    req.body = decryptedData ? JSON.parse(decryptedData) : {};
    next();
  } catch (error) {
    return { error: "Decryption failed" };
  }
}
