import mongoose from "mongoose";

const inventoryUpdateLogSchema = new mongoose.Schema(
  {
    fileName: {
      type: String,
    },
    uploadTime: {
      type: Date,
      default: new Date().toISOString(),
    },
    uploadedBy: {
      type: String,
    },
    skus: {
      type: Number,
    },
    uploadedSkus: [
      {
        materialSku: { type: String },
        ibdSku: { type: String },
        eanNo: { type: String },
        quantity: { type: Number },
      },
    ],
  },
  { timestamps: true }
);

const InventoryUpdateLog = mongoose.model(
  "InventoryUpdateLog",
  inventoryUpdateLogSchema
);
export default InventoryUpdateLog;
