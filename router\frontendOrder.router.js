import express from "express";
import {
  getOrdersForWebsite,
  getOneOrder,
  updateOrder,
  deleteOrder,
  getOrderByStatus,
  convertCartToOrder,
  generateOrderSheet,
  editOrderFromSheet,
} from "../controller/order.controller.js";
import { notifyStatusChange } from "../controller/common/index.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";

const frontendOrderRouter = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

frontendOrderRouter.route("/").get(getOrdersForWebsite);

frontendOrderRouter.route("/order-by-status").get(getOrderByStatus);

frontendOrderRouter.route("/cart/:cartId").post(convertCartToOrder);

frontendOrderRouter.route("/:id/generate_sheet").get(generateOrderSheet);
frontendOrderRouter
  .route("/:id/edit_sheet")
  .post(upload.single("file"), editOrderFromSheet);

frontendOrderRouter
  .route("/:id")
  .get(getOneOrder)
  .patch(notifyStatusChange, updateOrder)
  .delete(deleteOrder);

export default frontendOrderRouter;
