
import fs from 'fs'
import mongoose from 'mongoose'
import dotenv from 'dotenv'
import Status from '../model/status.model.js'
import path from "path";
import AppError from "./utils/appError.js";


const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);


dotenv.config({ path: './config.env' });

const DB = process.env.DATABASE.replace(
  '<PASSWORD>',
  process.env.DATABASE_PASSWORD
);

mongoose
  .connect(DB, {
    useNewUrlParser: true,
    useCreateIndex: true,
    useFindAndModify: false
  })
  .then(() => console.log('DB connection successful!'));

// READ JSON FILE
const status = JSON.parse(fs.readFileSync(`${__dirname}/status.json`, 'utf-8'));

// IMPORT DATA INTO DB
const importData = async () => {
  try {
    await Status.create(status);
    // console.log('Data successfully loaded!');
  } catch (err) {
    console.log(err);
  }
  process.exit();
};

// DELETE ALL DATA FROM DB
const deleteData = async () => {
  try {
    await Status.deleteMany();
    // console.log('Data successfully deleted!');
  } catch (err) {
    console.log(err);
  }
  process.exit();
};

if (process.argv[2] === '--import') {
  importData();
} else if (process.argv[2] === '--delete') {
  deleteData();
}