import express from "express";
import rateLimit from "express-rate-limit";
import helmet from "helmet";
import cors from "cors";
import * as dotenv from "dotenv";
import ApiRouter from "./router/index.js";
import path from "path";
import AppError from "./utils/appError.js";
import globalErrorHandler from "./utils/globalErrorHandler.js";
import cron from "node-cron";
import {
  checkSLABreach,
  emailSendCron,
  generateEscalationNotification,
} from "./service/cron/email.cron.js";
import * as fs from "fs";
import { generateInvoicePdf } from "./utils/pdfGenerator.js";
import SftpClient from "ssh2-sftp-client";
import inventorySyncShopify from "./service/shopify/inventorySyncShopify.service.js";
import { fetchAndSaveCompanies } from "./service/shopify/priceSyncShopify.service.js";
import { getCustomerAndPriceForAllSKUs } from "./service/shopify/metafieldUpdate.service.js";
import {
  deleteOldInventoryLogs,
  syncProductsAndVariants,
} from "./utils/scripts/index.js";
import ExpressMongoSanitize from "express-mongo-sanitize";
import { allowedOrigins } from "./constants/allowedOrigins.js";
import internalRouter from "./router/internalRouter.js";

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

const limiter = rateLimit({
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 1 * 60 * 1000,
  max: process.env.RATE_LIMIT_MAX,
  message: {
    status: 429,
    error: "Too many requests. Please try again later.",
  },
});

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) return callback(null, true);
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
};

const app = express();

app.use(
  helmet.contentSecurityPolicy({
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: [
        "'self'",
        "data:",
        "https://titand2d.s3.amazonaws.com",
        "https://cdn.shopify.com",
        "https://ps.w.org",
      ],
      connectSrc: [
        "'self'",
        "https://d2d.titan.in",
        "https://gvcfrfzta0.execute-api.ap-south-1.amazonaws.com",
      ],
      objectSrc: ["'none'"],
      frameAncestors: ["'none'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: [],
      scriptSrcAttr: ["'none'"],
      requireTrustedTypesFor: ["'script'"],
    },
  })
);

app.use(
  helmet.hsts({
    maxAge: 63072000,
    includeSubDomains: true,
    preload: true,
  })
);

app.use(helmet.frameguard({ action: "deny" }));
app.use(helmet.referrerPolicy({ policy: "no-referrer" }));
app.use(helmet.noSniff());
app.disable("x-powered-by");

app.set("trust proxy", 1);
app.use(cors(corsOptions));
app.set("view engine", "pug");
app.set("views", path.join(__dirname, "views"));
app.use("/dump", express.static(path.join(__dirname, "asset", "dump")));

app.use(express.json());
app.use(
  ExpressMongoSanitize({
    allowDots: true,
    replaceWith: "_",
  })
);

// app.use(express.json({ limit: "100kb" }));

const handleCreateInvoice = async (req, res, next) => {
  try {
    const { moreDetails, client, items, paid, debugMode } = req.body;

    const invoiceNumber = "FACT-";

    // Calculate sum per item
    items.forEach((item) => {
      item.amountSum = item.price * item.quantity;
      return item;
    });

    // getting subtotal ->
    const subtotal = items.reduce((prev, curr) => {
      return curr.amountSum + prev;
    }, 0);

    const fileName = invoiceNumber + ".pdf";
    const filePath = path.join(__dirname, `/asset/static/${fileName}`);

    const invoiceDetails = { items, invoiceNumber, paid, subtotal, client };

    generateInvoicePdf(invoiceDetails, filePath);

    const files = [filePath];

    return res.send({ success: true, data: { files } });
  } catch (err) {
    console.error(err);
    return res.status(400).send({ message: err.message });
  }
};

app.post("/generate-pdf", handleCreateInvoice);

cron.schedule("*/5 * * * *", () => {
  console.log("Running email cron job");
  emailSendCron();
});

// not in use as of now
// cron.schedule("0 0 * * *", () => {
//   console.log("Running inventory sync cron job 12 AM daily");
//   inventorySyncShopify();
// });

cron.schedule("0 0 8 * * *", () => {
  console.log("Running escalation cron job at 8:00 AM every day...");
  generateEscalationNotification();
});

// sla is not in use as of now
cron.schedule("*/5 * * * *", () => {
  console.log("Running SLA Breach cron job every 5 minutes...");
  checkSLABreach();
});

cron.schedule("0 1 * * *", () => {
  console.log("Running catalog, customer and price sync cron job");
  fetchAndSaveCompanies();
});

cron.schedule("0 2 * * *", () => {
  console.log("Running metafield update cron job");
  getCustomerAndPriceForAllSKUs();
});
cron.schedule("30 1 * * *", () => {
  console.log("Running product sync task at 1:30 AM");
  syncProductsAndVariants();
});

cron.schedule(
  "0 2 * * *",
  () => {
    console.log("Clearing Older Logs at 2 AM IST");
    deleteOldInventoryLogs();
  },
  {
    timezone: "Asia/Kolkata",
  }
);

getCustomerAndPriceForAllSKUs();

export const downloadFileFromSFTP = async (
  sftpConfig,
  remotePath,
  localPath
) => {
  // const sftpConfig = {
  //   host: "************",
  //   username: process.env.SFTP_USERNAME,
  //   port: "22",
  //   password: process.env.SFTP_PASSWORD,
  //   remoteDir: req.body.path,
  // };
  // console.log(remotePath, "REMOTE PATH");
  // console.log(localPath, "LOCAL PATH");
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    await sftp.get(remotePath, localPath);
    return { code: 0, message: "File downloaded" };
  } catch (error) {
    return error;
  } finally {
    await sftp.end();
  }
};

app.get("/download", async (req, res) => {
  const sftpConfig = {
    host: "************",
    username: process.env.SFTP_USERNAME,
    port: "22",
    password: process.env.SFTP_PASSWORD,
    remoteDir: req.body.path,
  };
  const filePath = decodeURI(req.query.filePath) || req.query.filePath;
  const localFilePath = path.join(
    __dirname,
    "asset",
    "dump",
    `${filePath.split("/").pop()}`
  );
  // console.log("localFilePath", localFilePath);
  // console.log("localFilePath", localFilePath);

  const apiAuth = req.query.auth;
  if (!apiAuth || apiAuth != "2XCF_786Ja19038_5hree_8376Ram837") {
    return res.send({
      code: 786,
      error: "Missing Auth Token",
    });
  }
  await fs.promises.writeFile(localFilePath, "");
  const remotePath = `${filePath}`;
  await downloadFileFromSFTP(sftpConfig, remotePath, localFilePath);
  if (!filePath) {
    return res.status(400).send("filePath query parameter is required");
  }
  // console.log(filePath, "filePath==========================>");
  // console.log(
  //   decodeURI(filePath),
  //   "decodeURIfilePath==========================>"
  // );

  // const sftp = new SftpClient();

  try {
    // const files = fs.readdirSync(publicFolderPath);
    // const transformedFileDetails = files.map((file) => {
    //   return {
    //     fileName: file,
    //     downloadUrl: `${process.env.APP_URL}/dump/${file}`
    //   };
    // });
    res.status(200).send({
      data: `${process.env.APP_URL}/dump/${filePath.split("/").pop()}`,
    });
    // await sftp.connect(sftpConfig);
    // sftp.get(filePath)
    //   .then((stream) => {
    //     res.setHeader('Content-Disposition', `attachment; filename="${filePath.split('/').pop()}"`);
    //     res.setHeader('Content-Type', 'application/octet-stream');

    //     stream.pipe(res);
    //   })
    //   .catch(err => {
    //     console.error('Error fetching file from SFTP:', err);
    //     res.status(500).send('Error fetching file from SFTP');
    //   });
  } catch (err) {
    console.error("SFTP connection error:", err);
    res.status(500).send("SFTP connection error");
  }
});

app.post("/sftp", async (req, res, next) => {
  // console.log("PAYLOAD", req.body.path);
  console.log(
    "process.env.SFTP_USERNAME_PRODUCTION",
    process.env.SFTP_USERNAME_PRODUCTION
  );
  console.log(
    "process.env.SFTP_PASSWORD_PRODUCTION",
    process.env.SFTP_PASSWORD_PRODUCTION
  );

  const sftpConfig = {
    host: "************",
    username: process.env.SFTP_USERNAME,
    port: "22",
    password: process.env.SFTP_PASSWORD,
    remoteDir: req.body.path,
  };
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    console.log("sftp connected");
    const payload = req.body;
    if (!payload.method) {
      return res.send({
        code: 786,
        message:
          "method is missing in payload, following are the possible values [GET, DOWNLOAD]",
      });
    }
    if (!payload.path) {
      return res.send({
        code: 786,
        message:
          "path is missing in payload, Example: /home/<USER>/DEV/Indent/Process",
      });
    }
    if (payload.method.toLowerCase() == "get") {
      console.log("PAYLOAD INSIDE GET IF CONDITION", req.body.path);

      console.log(await sftp.list(req.body.path), "success list inside get");
      const sftpFiles = await sftp.list(req.body.path);
      return res.send({
        code: 0,
        data: sftpFiles,
        fileNames: sftpFiles.map((it) => it.name),
      });
    }
  } catch (error) {
    await sftp.end();
    console.log(error, "catch");
    res.send({
      code: 786,
      error: error,
    });
    return error;
  } finally {
    await sftp.end();
  }
});

app.use("/api", limiter);

app.use("/api", ApiRouter);

app.use("/internal", internalRouter);
app.get("/healthcheck", (req, res) => {
  console.log("health test");
  res.status(200).json({ status: "Health Status - OK!!!" });
});

app.all("*", (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this Server`, 404));
});

app.use(globalErrorHandler);

export default app;
