import mongoose from "mongoose";

const distributorSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
    },
    shopifyCompanyId: {
      type: Number,
      required: true,
    },
    shopifyCustomerId: {
      type: Number,
    },
    email: {
      type: String,
    },
    phone: {
      type: String,
    },
    priority: {
      type: Number,
    },
    country: {
      type: String,
    },
    countryManager: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "DepartmentType",
      },
    ],
    internalCode: {
      type: String,
    },
    customerSince: {
      type: String,
    },
    externalId: {
      type: String,
    },
    note: {
      type: String,
    },
    companyEmail: {
      type: String,
    },
    companyFirstName: {
      type: String,
    },
    companyLastName: {
      type: String,
    },
    companyPhone: {
      type: String,
    },
    companyName: {
      type: String,
    },
    companyLocale: {
      type: String,
    },
    adresses: {
      type: Array,
    },
    paymentMethods: {
      type: String,
    },
    locations: {
      type: Array,
    },
    password: {
      type: String,
    },
    plant: {
      type: String,
    },
    storage_location: {
      type: String,
    },
    customer_id: {
      type: String,
    },
  },
  { timestamps: true }
);

const Distributor = mongoose.model("Distributor", distributorSchema);
export default Distributor;
