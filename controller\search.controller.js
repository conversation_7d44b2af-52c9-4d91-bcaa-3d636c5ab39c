import Distributor from "../model/distributor.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import mongoose from "mongoose";

export async function searchApi(req, res) {
  const {
    input,
    fromDate,
    toDate,
    distributors = [],
    page = 1,
    pageSize = 50,
  } = req.body;

  const pipeline = [];

  const isValidDate = (dateString) => !isNaN(Date.parse(dateString));

  try {
    // Country Manager Filter
    const isCountryManager =
      req.user?.designation?.isCountryManager &&
      Array.isArray(req.user.distributor);

    if (isCountryManager) {
      const countryManagerDistributorIds = req.user.distributor.map(
        (id) => new mongoose.Types.ObjectId(id)
      );
      pipeline.push({
        $match: {
          distributor: { $in: countryManagerDistributorIds },
        },
      });
    }

    // Input search
    if (input) {
      const statusResult = await Status.findOne({
        status: input.toUpperCase(),
      });

      const statusCondition = statusResult
        ? { status: statusResult._id }
        : null;
      const nameCondition = { name: input };

      const conditions = [];

      if (statusCondition) conditions.push(statusCondition);
      conditions.push(nameCondition);

      pipeline.push({
        $match: {
          $or: conditions,
        },
      });
    }

    // Distributors filter (if not country manager or explicitly filtering within country manager’s list)
    if (distributors.length > 0) {
      const distributorIds = distributors.map(
        (id) => new mongoose.Types.ObjectId(id)
      );

      pipeline.push({
        $match: {
          distributor: { $in: distributorIds },
        },
      });
    }

    // Date range filter
    if (fromDate || toDate) {
      const dateMatch = {};
      if (fromDate && isValidDate(fromDate)) {
        dateMatch.$gte = new Date(fromDate);
      }
      if (toDate && isValidDate(toDate)) {
        dateMatch.$lte = new Date(toDate);
      }

      if (Object.keys(dateMatch).length > 0) {
        pipeline.push({
          $match: {
            createdAt: dateMatch,
          },
        });
      }
    }

    // Lookups
    pipeline.push(
      {
        $lookup: {
          from: "status",
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      {
        $lookup: {
          from: "distributors",
          localField: "distributor",
          foreignField: "_id",
          as: "distributor",
        },
      },
      {
        $unwind: { path: "$status", preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: {
          path: "$distributor",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          status: {
            status: 1,
            colorCode: 1,
          },
          distributor: {
            firstName: 1,
            lastName: 1,
            name: 1,
            country: 1,
          },
          line_items: {
            $map: {
              input: "$line_items",
              as: "item",
              in: {
                quantity: "$$item.quantity",
                price: "$$item.price",
              },
            },
          },
          createdAt: 1,
          updatedAt: 1,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize }
    );

    const orders = await Order.aggregate(pipeline);

    // For counting total (exclude pagination stages)
    const countPipeline = pipeline.slice(0, -4);
    countPipeline.push({ $count: "total" });

    const totalResult = await Order.aggregate(countPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    res.send({ total, page, pageSize, orders });
  } catch (error) {
    console.error("Error fetching orders:", error);
    res.status(500).send("Something went wrong.");
  }
}
