import express from "express";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";
import {
  getAction,
  createAction,
  getOneAction,
  updateAction,
  deleteAction,
  changeStatusAction,
  triggerBatchProcessOrders,
  alignmentPendingOrderProcess,
} from "../controller/action.controller.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";
import { getCurrentProcessStatus } from "../middlewares/getCurrentBulkProcessStatus.js";
import {
  getCurrentRunningStatus,
  acknowledgeProcessing,
} from "../controller/bulkProcess.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

router
  .route("/")
  .get(getAction)
  .post(authenticateAdminPeopleAccess("statusFlow", "read"), createAction);
router.post(
  "/change_status",
  authenticateDepttPeopleAccess(),
  upload.single("file"),
  changeStatusAction
);
router.post(
  "/process_orders",
  authenticateDepttPeopleAccess("order", "write"),
  getCurrentProcessStatus,
  triggerBatchProcessOrders
);

router.get(
  "/process_orders/status",
  authenticateDepttPeopleAccess("order", "read"),
  getCurrentRunningStatus
);
router.post(
  "/process_pending_orders",
  authenticateDepttPeopleAccess(),
  alignmentPendingOrderProcess
);
router.post(
  "/process_orders/acknowledge",
  authenticateDepttPeopleAccess("order", "read"),
  acknowledgeProcessing
);

router
  .route("/:id")
  .get(getOneAction)
  .patch(authenticateAdminPeopleAccess("statusFlow", "write"), updateAction)
  .delete(authenticateAdminPeopleAccess("statusFlow", "delete"), deleteAction);

export default router;
