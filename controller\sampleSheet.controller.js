import ExcelJS from "exceljs";
import sampleSheetData from "../constants/sampleSheetData.js";

const generateSampleSheetFile = async (req, res) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Sample Sheet");
    worksheet.addRow(["ProductCode", "Quantity"]);
    sampleSheetData.forEach((row) => {
      worksheet.addRow([row.product, row.quantity]);
    });

    worksheet.columns = [
      { header: "ProductCode", key: "product_code", width: 20 },
      { header: "Quantity", key: "quantity", width: 20 },
    ];

    const fileName = "SampleLineSheet_Data.xlsx";
    const buffer = await workbook.xlsx.writeBuffer();

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);

    res.send(buffer);
  } catch (error) {
    console.error("Error generating Excel file:", error);
    res.status(500).json({
      responseCode: 1,
      status: "error",
      message:
        "Something went wrong while generating the file. Please try again later.",
    });
  }
};

export default generateSampleSheetFile;
