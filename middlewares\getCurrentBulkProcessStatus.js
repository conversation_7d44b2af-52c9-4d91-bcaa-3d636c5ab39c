import BulkProcessingStatus from "../model/bulkProcessing.model.js";

export const getCurrentProcessStatus = async (req, res, next) => {
  try {
    const foundOrderProcessStatus = await BulkProcessingStatus.findOne({
      type: "Order Process",
    });

    if (!foundOrderProcessStatus) {
      next();
    }

    const isProcessRunning = foundOrderProcessStatus?.status;

    if (isProcessRunning === "RUNNING") {
      return res.status(200).json({
        isProcessRunning: true,
        error: false,
      });
    }

    next();
  } catch (err) {
    console.log(" Error in getting the current Status of the process", err);
  }
};
