import express from "express";
import {
  createDistributor,
  getOneDistributor,
  updateDistributor,
  deleteDistributor,
  updateDistributorPriority,
  createCustomerInShopify,
  checkIfDistributorHasOrderOrNot,
  removeEmailFromPayload,
  updateDistributorInShopify,
  getCountryManagerDistributors,
} from "../controller/distributor.controller.js";
import { generateToken } from "../utils/helperFunction.js";
import { hashPassword } from "../controller/department.controller.js";
import authenticateDepttPeopleAccess, { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

const generateAuthToken = (req, res, next) => {
  const token = generateToken({
    shopifyCustomerId: req.body.shopifyCustomerId,
    customerType: "distributor",
  });
  req.body.token = token;
  next();
};

router
  .route("/")
  .get(
    authenticateDepttPeopleAccess("distributor", "read"),
    getCountryManagerDistributors
  )
  .post(
    authenticateAdminPeopleAccess("distributor", "write"),
    createCustomerInShopify,
    hashPassword,
    generateAuthToken,
    createDistributor
  );

router
  .route("/priority/update")
  .patch(
    authenticateAdminPeopleAccess("distributor", "write"),
    updateDistributorPriority
  );

router
  .route("/:id")
  .get(authenticateDepttPeopleAccess("distributor", "read"), getOneDistributor)
  .patch(
    authenticateAdminPeopleAccess("distributor", "write"),
    removeEmailFromPayload,
    updateDistributorInShopify,
    hashPassword,
    updateDistributor
  )
  .delete(
    authenticateAdminPeopleAccess("distributor", "delete"),
    checkIfDistributorHasOrderOrNot,
    deleteDistributor
  );

export default router;
