import express from "express";
import {
  getDepartmentsType,
  createDepartmentType,
  getOneDepartmentType,
  updateDepartmentType,
  deleteDepartmentType,
} from "../controller/departmentType.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";
const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("department", "read"), getDepartmentsType)
  .post(
    authenticateAdminPeopleAccess("department", "write"),
    createDepartmentType
  );

router
  .route("/:id")
  .get(
    authenticateAdminPeopleAccess("department", "read"),
    getOneDepartmentType
  )
  .patch(
    authenticateAdminPeopleAccess("department", "write"),
    updateDepartmentType
  )
  .delete(
    authenticateAdminPeopleAccess("department", "delete"),
    deleteDepartmentType
  );

export default router;
