import express from "express";
import {
  alignmentPendingLineSheet,
  generateLineSheet,
} from "../controller/linesheet.controller.js";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";
import { authenticateFrontendRouter } from "../middlewares/authenticateFrontendRouter.js";

const router = express.Router();

router
  .route("/download/shipments")
  .get(
    authenticateDepttPeopleAccess("shipment", "read"),
    alignmentPendingLineSheet
  );
router
  .route("/download_line_sheet")
  .post(generateLineSheet);

export default router;
