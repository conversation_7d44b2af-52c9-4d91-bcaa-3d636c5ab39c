name: Deploy Titan-d2d live-uat - Backend

on:
  push:
    branches: [prod-master]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🛠 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 🔐 Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.STC_AWS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.STC_AWS_HOST_IP }} >> ~/.ssh/known_hosts

      - name: 🚀 SSH Deploy Backend
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.STC_AWS_USERNAME }}@${{ secrets.STC_AWS_HOST_IP }} << 'EOF'
            set -e
            cd /home/<USER>/d2d-staging/titan-d2d-backend
            touch .env
            echo "${{ secrets.UAT_ENV }}" > .env
            git pull origin prod-master
            npm i
            pm2 restart live-backend
            pm2 save
          EOF
