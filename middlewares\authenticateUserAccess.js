import User from "../model/user.model.js";
import Department from "../model/department/department.model.js";
import mongoose from "mongoose";
import jwt from "jsonwebtoken";

let scopes = {
  department: ["read", "write", "delete"],
  designation: ["read", "write", "delete"],
  distributor: ["read", "write", "delete"],
  organization: ["read", "write", "delete"],
  order: ["read", "write", "delete"],
  shipment: ["read", "write", "delete"],
  status: ["read", "write", "delete"],
  statusFlow: ["read", "write", "delete"],
  user: ["read", "write", "delete"],
  user_group: ["read", "write", "delete"],
};

const authenticateUserAccess = (module, scope) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      return res.status(401).json({ error: "No token provided" });
    }

    const token = auth.split(" ")[1];

    try {
      const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
      let userId = decodedUser.userId;

      const userType = decodedUser.type;

      if (userType === "deptt_people") {
        if (["order", "shipment"].includes(module) && scope === "read") {
          const user = await Department.findById(userId).populate();
          if (!user) {
            return res.status(401).json({ error: "Invalid token" });
          }

          req.body.current_user = user;
          next();
        }
      }
      let user = await User.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(userId),
          },
        },
        {
          $addFields: {
            usergroupids: {
              $map: {
                input: "$userGroup",
                as: "group",
                in: {
                  $toObjectId: "$$group",
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: "usergroups",
            localField: "usergroupids",
            foreignField: "_id",
            as: "userGroups",
          },
        },
        {
          $project: {
            userGroups: 1,
          },
        },
      ]);

      if (!user.length) {
        return res.status(401).json({ error: "Invalid token" });
      }
      let scopes = user[0].userGroups.flatMap((x) => {
        return x.access_scopes[module];
      });
      // console.log(user[0].userGroups,scope)
      if (scopes.includes(scope)) {
        req.body.current_user = decodedUser;
        next();
      } else {
        return res.status(403).json({ error: "Forbidden" });
      }
    } catch (err) {
      return res.status(401).json({ error: "Invalid token" });
    }
  };
};

export default authenticateUserAccess;
