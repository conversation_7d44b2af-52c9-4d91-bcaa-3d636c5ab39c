import mongoose from "mongoose";
import jwt from "jsonwebtoken";
import Department from "../model/department.model.js";
import User from "../model/user.model.js";
import Distributor from "../model/distributor.model.js";
import UserGroup from "../model/usergroup.model.js";
import { defaultAccessScops } from "../constants/accessScopes.js";

const authenticateDepttPeopleAccess = (module, scope) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      return res.status(401).json({ error: "No token provided" });
    }
    const token = auth.split(" ")[1];

    try {
      const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
      const userId = decodedUser.userId;
      const [department, admin, distributor] = await Promise.all([
        Department.findById(userId),
        User.findById(userId),
        Distributor.findById(userId),
      ]);

      let user;

      if (admin) {
        const { userGroup } = admin;

        const groups = await UserGroup.find({
          _id: { $in: userGroup },
        });

        if (groups?.length === 0) {
          return res.status(401).json({ error: "You're not authorized." });
        }

        const hasPermissions =
          groups.some((role) => {
            if (role.access_scopes[module]) {
              return role.access_scopes[module].includes(scope);
            } else {
              return false;
            }
          }) || defaultAccessScops[module].includes(scope);

        if (!hasPermissions) {
          return res.status(401).json({ error: "You're not authorized." });
        }

        req.body.current_user = admin;
        req.user = admin;
        return next();
      }

      user = department || distributor;
      if (!user) {
        return res.status(401).json({ error: "You're not authorized." });
      }

      req.body.current_user = user;
      req.user = user;
      next();
    } catch (err) {
      console.log("error:", err);
      return res.status(401).json({ error: "You're Not Authorized" });
    }
  };
};

/* 
      
        The module will be passed.
        The module passed should be of the following

        department
        designation
        distributor
        company
        order
        shipment
        organization
        status
        user
        statusFlow
        user_group
        people
        deptt_people
      
      */

export const authenticateAdminPeopleAccess = (module, scope) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      return res.status(401).json({ error: "No token provided" });
    }

    const token = auth.split(" ")[1];
    try {
      const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
      const userId = decodedUser.userId;
      const user = await User.findById(userId);

      if (!user) {
        return res.status(401).json({ error: "You're not authorized." });
      }
      const { userGroup } = user;
      const userRoles = await UserGroup.find(
        {
          _id: { $in: userGroup },
        },
        { access_scopes: 1 }
      );

      if (userRoles?.length === 0) {
        return res.status(401).json({ error: "You're not authorized." });
      }

      const hasPermissions = userRoles.some((role) => {
        if (role.access_scopes[module]) {
          return role.access_scopes[module].includes(scope);
        } else {
          return false;
        }
      });

      if (!hasPermissions) {
        return res.status(401).json({ error: "You're not authorized." });
      }
      req.body.current_user = user;
      req.user = user;
      next();
    } catch (err) {
      return res.status(401).json({ error: "You're Not Authorized" });
    }
  };
};

export default authenticateDepttPeopleAccess;
