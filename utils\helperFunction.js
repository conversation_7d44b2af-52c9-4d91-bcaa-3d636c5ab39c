import Inventory from "../model/inventory.model.js";
import AppError from "./appError.js";
import AWS from "aws-sdk";
import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";
import { month } from "../controller/shipment.controller.js";
const s3 = new AWS.S3({ signatureVersion: "v4" });

export const toScreamingSnakeCase = (word) => {
  if (typeof word !== "string") throw new Error("Input must be a string");
  return word
    .trim()
    .replace(/[\s\-]+/g, "_")
    .toUpperCase();
};

export const filterObj = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((element) => {
    if (allowedFields.includes(element)) newObj[element] = obj[element];
  });
  return newObj;
};

export const removeObjectKeys = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((element) => {
    if (!allowedFields.includes(element)) newObj[element] = obj[element];
  });
  return newObj;
};

export const generateRandomNumber = () => {
  const minm = 10000;
  const maxm = 99999;
  return Math.floor(Math.random() * (maxm - minm + 1)) + minm;
};

export const validateProductSheetHeader = (data) => {
  const availableHeaderKeys = Object.keys(data);
  const allowedHeaderKeys = ["ProductCode", "Quantity"];
  // const allowedHeaderKeys = ['brand', 'ProductCode', 'skuType', 'Quantity', 'ucp', 'variantId'];
  const invalidKeys = allowedHeaderKeys.filter((headerKey) => {
    return !availableHeaderKeys.includes(headerKey);
  });
  return invalidKeys;
};

export const validateProductSheet = async (data) => {
  // const missingQuantitySkus = data.filter((item) => {
  //   return !item.Quantity;
  // }).map((it) => {
  //   return it.ProductCode;
  // });
  // if (missingQuantitySkus.length > 0) return { returnCode: 5, message: `Missing Quantity for following skus [${missingQuantitySkus}]` };
  const skuList = data
    .filter((it) => {
      // console.log((it))
      return it.Quantity && it.Quantity > 0;
    })
    .map((item) => {
      return item.ProductCode;
    });
  if (skuList <= 0) return { returnCode: 5, message: `Missing Quantity` };

  const inventoryData = await Inventory.find({ sku: { $in: skuList } });
  return {
    returnCode: 0,
    inventoryData,
  };
};

export const uploadToS3 = async (filePath, bucketName, fileName) => {
  try {
    const fileContent = fs.readFileSync(filePath);
    const fileExtension = path.extname(filePath);
    const newFileName = fileName
      ? fileName
      : `line_sheet_${Date.now().toString()}${fileExtension}`;
    const params = {
      Bucket: bucketName,
      Key: newFileName,
      Body: fileContent,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, {}, (error, data) => {
        console.log(error);
        if (error) {
          reject(error);
          return;
        }
        resolve(data);
      });
    });
  } catch (error) {
    console.log(error, "s3 error");
    return {
      status: "fail",
      error,
    };
  }
};

export function generateSignedURL(bucket, key, expiresIn = 3600) {
  const params = {
    Bucket: bucket,
    Key: key,
    Expires: expiresIn, // in seconds
  };

  return s3.getSignedUrlPromise("getObject", params);
}

export const extractKeyFromURL = (url) => {
  if (!url) {
    return undefined;
  }
  
  const urlObject = new URL(url);
  return urlObject.pathname.slice(1);
};
export const generateToken = (payload) => {
  return jwt.sign(
    {
      ...payload,
    },
    process.env.JWT_TOKEN_SECRET,
    {
      expiresIn: "1d",
    }
  );
};

export const validateAndCreateInventory = async (data) => {
  const notHaveInventory = [];
  const skuList = data?.map((item) => item.ProductCode);
  const productCodeQuantity = new Map();
  const existingInventory = await Inventory.find(
    {
      sku: { $in: skuList },
    },
    { sku: 1, _id: 1 }
  );

  const existingInventoryMap = new Map();

  existingInventory.forEach((item) => {
    existingInventoryMap.set(item.sku, item._id);
  });

  const skuToOriginalCode = new Map();

  data.forEach((item) => {
    const { ProductCode, Quantity, Image } = item;
    // Check if Title and Quantity are present and valid
    if (ProductCode || Quantity || Quantity >= 0) {
      productCodeQuantity.set(ProductCode, Quantity);

      const existingInventory = existingInventoryMap.get(ProductCode);

      if (!existingInventory) {
        const randomSku = `${ProductCode.toString()
          .split(" ")
          .join("_")}_${Math.floor(Math.random() * 100000)}`;

        // Prepare the payload for creating a new inventory item
        const lineItemPayload = {
          shopifyVariantId: "n/a",
          shopifyProductId: "n/a",
          productTitle: ProductCode.toString(),
          variantTitle: ProductCode.toString(),
          inventoryType: "CUSTOM",
          description: `Inventory for ${ProductCode}`,
          price: 0,
          sku: randomSku, // SKU logic
          sapSkus: [{ sku: randomSku, eanNo: "", quantity: 0, onHold: 0 }],
          quantity: 0,
          image: Image
            ? Image
            : "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
        };

        skuToOriginalCode.set(randomSku, ProductCode);

        notHaveInventory.push({ insertOne: { document: lineItemPayload } });
      }
    } else {
      console.log(
        `Missing or invalid Title/Quantity for product with code: ${ProductCode}`
      );
    }
  });

  const result = await Inventory.bulkWrite(notHaveInventory);

  const insertedIds = Object.values(result.insertedIds);

  const insertedDocs = await Inventory.find({
    _id: { $in: insertedIds },
  });

  const updatedInventoryItems = insertedDocs.map((product) => {
    const productCode = skuToOriginalCode.get(product.sku);
    const productQuantity = productCodeQuantity.get(productCode);
    return {
      ...product.toObject(),
      quantity: productQuantity,
    };
  });

  return updatedInventoryItems;
};

export const getCountry = (distributor) => {
  return distributor?.country?.substring(0, 3).toUpperCase() || "IND";
};

export const formattedMonthAndYear = (orderCreatedAt) => {
  if (orderCreatedAt) {
    const date = new Date(orderCreatedAt);
    const monthOfTheYear = month[date.getMonth()];
    const year = date.getFullYear().toString().slice(-2);

    return `${monthOfTheYear}${year}`;
  } else {
    return;
  }
};
