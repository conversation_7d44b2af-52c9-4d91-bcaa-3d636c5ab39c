import Company from "../../model/company.model.js";
import Inventory from "../../model/inventory.model.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";

// Function to update metafields in Shopify
export const updateMetaFields = async (ownerId, customerPricing) => {
  const result = await fetchGraphqlDataShopify(UPDATE_VARIANT_META_FIELDS, {
    ownerId: ownerId, // ProductVariant ID
    key: "custom", // The key to identify the metafield
    value: JSON.stringify(customerPricing), // Customer pricing as JSON
    type: "json",
    namespace: "customer_pricing", // Namespace for the metafield
  });
  return result;
};

// Function to get customer and price data for all SKUs in the inventory
export const getCustomerAndPriceForAllSKUs = async () => {
  try {
    const inventories = await Inventory.find({});

    if (!inventories || inventories.length === 0) {
      throw new Error("No inventory data found");
    }

    for (const inventory of inventories) {
      const { sku, priceList, shopifyVariantId } = inventory;

      if (!priceList || priceList.length === 0) {
        console.log(`No price list found for SKU: ${sku}`);
        continue;
      }

      const result = [];

      for (let priceEntry of priceList) {
        const { companyLocationCatalogId, price, currency } = priceEntry;

        const matchingCompanies = await Company.find({
          companyLocationCatalogIds: companyLocationCatalogId,
        });

        matchingCompanies.forEach((company) => {
          company.customerIds.forEach((customerId) => {
            result.push({
              customerId: customerId,
              price: price,
              currency: currency,
            });
          });
        });
      }

      if (result.length > 0) {
        const customerPricing = result.reduce((acc, { customerId, price }) => {
          acc[customerId] = price;
          return acc;
        }, {});

        const metaFieldResult = await updateMetaFields(
          `gid://shopify/ProductVariant/${shopifyVariantId}`,
          customerPricing
        );

        if (
          metaFieldResult.data &&
          metaFieldResult.data.metafieldsSet.userErrors.length === 0
        ) {
          console.log(
            `Metafield for price updated successfully for SKU: ${sku}`
          );
        } else {
          console.error(
            `Error updating metafield for SKU: ${sku}`,
            metaFieldResult.data.metafieldsSet.userErrors
          );
        }
      } else {
        console.log(`No matching company or customer data for SKU: ${sku}`);
      }
    }
  } catch (error) {
    console.error(
      "Error fetching or processing inventory data:",
      error.message
    );
    throw new Error("Could not process customer and price data for all SKUs");
  }
};

const UPDATE_VARIANT_META_FIELDS = `mutation MetafieldsSet($ownerId: ID!, $key: String!, $value: String!, $type: String!, $namespace: String!) {
  metafieldsSet(
    metafields: {
      ownerId: $ownerId,
      key: $key,
      value: $value,
      type: $type,
      namespace: $namespace
    }
  ) {
    userErrors {
      message
    }
  }
}`;
