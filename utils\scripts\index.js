import Inventory from "../../model/inventory.model.js";
import fetchGraphqlDataShopify from "../fetchGraphqlDataShopify.js";
import InventoryUpdateLog from "../../model/inventoryLogs.model.js";

const extractShopifyId = (gid) => gid.split("/").pop();

const fetchProductsAndMetafields = async (cursor = null) => {
  const response = await fetchGraphqlDataShopify(GET_PRODUCTS_WITH_METAFIELDS, {
    productCursor: cursor,
  });

  const { nodes: products, pageInfo } = response.data.products;

  // Map the product data with metafields
  const productData = products.map((product) => ({
    id: product.id,
    title: product.title, // Added title to product mapping
    status: product.status,
    metafields: {
      brand: product.m1?.value || null,
      cluster: product.m2?.value ? product.m2.value : null,
      gender: product.m3?.value ? product.m3.value : null,
      collection: product.m4?.value || null,
      hsnCode: product.m5?.value || null,
      caseMaterial: product.caseMaterial?.value || null,
      caseWidth: product.caseWidth?.value || null,
      caseLength: product.caseLength?.value || null,
      caseThickness: product.caseThickness?.value || null,
      strapMaterial: product.strapMaterial?.value || null,
      dialColor: product.dialColor?.value || null,
      function: product.function?.value || null,
      pmrType: product.pmrType?.value || null,
    },
  }));

  return { productData, pageInfo };
};

const fetchProductVariants = async (productId, cursor = null) => {
  const response = await fetchGraphqlDataShopify(GET_PRODUCT_VARIANTS, {
    productId,
    variantCursor: cursor,
  });

  const { edges: variants, pageInfo } = response.data.product.variants;
  const { nodes: images } = response.data.product.images; // Fetch images

  const variantData = variants.map((edge) => ({
    id: edge.node.id,
    title: edge.node.title,
    price: edge.node.price,
    sku: edge.node.sku,
    inventoryQuantity: edge.node.inventoryQuantity,
    image:
      images?.[0]?.src || // Use the first product image if available
      "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
  }));

  return { variantData, pageInfo };
};

export const syncProductsAndVariants = async () => {
  let productCursor = null;
  let hasNextPage = true;

  // Loop through all products with pagination
  while (hasNextPage) {
    const { productData, pageInfo } = await fetchProductsAndMetafields(
      productCursor
    );

    // Iterate through each product to fetch its variants
    for (const product of productData) {
      let variantCursor = null;
      let variantHasNextPage = true;
      let allVariants = [];

      // Fetch all variants for the product using pagination
      while (variantHasNextPage) {
        const { variantData, pageInfo: variantPageInfo } =
          await fetchProductVariants(product.id, variantCursor);
        allVariants.push(...variantData);

        // Update variant pagination info
        variantCursor = variantPageInfo.endCursor;
        variantHasNextPage = variantPageInfo.hasNextPage;
      }

      // Prepare product payload for the database
      const productPayload = allVariants.map((variant) => {
        return {
          shopifyVariantId: extractShopifyId(variant.id),
          shopifyProductId: extractShopifyId(product.id),
          productTitle: product.title || "Unknown Product",
          variantTitle: variant.title || variant.sku,
          price: variant.price,
          sku: variant.sku,
          brand: product.metafields.brand,
          cluster: product.metafields.cluster,
          gender: product.metafields.gender,
          collection: product.metafields.collection,
          hsnCode: product.metafields.hsnCode,
          caseMaterial: product.metafields.caseMaterial,
          caseWidth: product.metafields.caseWidth,
          caseLength: product.metafields.caseLength,
          caseThickness: product.metafields.caseThickness,
          strapMaterial: product.metafields.strapMaterial,
          dialColor: product.metafields.dialColor,
          function: product.metafields.function,
          pmrType: product.metafields.pmrType,
          quantity: variant.inventoryQuantity,
          image: variant.image,
          status: product.status
        };
      });

      // Prepare bulk operations for database insertion
      const bulkOps = productPayload.map((variant) => {
        return {
          updateOne: {
            filter: { sku: variant.sku },
            update: { $set: variant },
            upsert: true,
          },
        };
      });

      // Execute the bulkWrite operation to sync variants to the database
      const result = await Inventory.bulkWrite(bulkOps);
    }

    // Update product pagination info
    productCursor = pageInfo.endCursor;
    hasNextPage = pageInfo.hasNextPage;
  }
  
  console.log("All products and variants synced successfully");
};

export const deleteOldInventoryLogs = async () => {
  try {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    const result = await InventoryUpdateLog.deleteMany({
      createdAt: { $lt: oneMonthAgo }
    });

    console.log(`${result.deletedCount} old logs deleted`);
  } catch (error) {
    console.error('Error deleting old logs:', error);
  }
};


const GET_PRODUCTS_WITH_METAFIELDS = `
  query getProductsWithMetafields($productCursor: String) {
    products(first: 100, after: $productCursor) {
      nodes {
        id
        title
        status
        m1: metafield(namespace: "custom", key: "brand") {
          value
          key
        }
        m2: metafield(namespace: "custom", key: "cluster") {
          value
          key
        }
        m3: metafield(namespace: "custom", key: "gender") {
            value
            key
         }
        m4: metafield(namespace: "custom", key: "collection") {
            value
            key
         }
        m5: metafield(namespace: "custom", key: "hsn_code") {
            value
            key
         }
        caseMaterial: metafield(namespace: "custom", key: "case_material") {
            value
            key
         }
        caseWidth: metafield(namespace: "custom", key: "case_width") {
            value
            key
         }
        caseLength: metafield(namespace: "custom", key: "case_length") {
            value
            key
         }
        caseThickness: metafield(namespace: "custom", key: "case_thickness") {
            value
            key
         }
        strapMaterial: metafield(namespace: "custom", key: "strap_material") {
            value
            key
         }
        dialColor: metafield(namespace: "custom", key: "dial_colour") {
            value
            key
         }

        function: metafield(namespace: "custom", key: "function") {
            value
            key
         }
        pmrType: metafield(namespace: "custom", key: "pmr_type") {
            value
            key
         }
        


        
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

const GET_PRODUCT_VARIANTS = `
  query getProductVariants($productId: ID!, $variantCursor: String) {
    product(id: $productId) {
      variants(first: 100, after: $variantCursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            price
            sku
            inventoryQuantity
          }
        }
      }
      images(first: 1) {
        nodes {
          src
        }
      }
    }
  }
`;
