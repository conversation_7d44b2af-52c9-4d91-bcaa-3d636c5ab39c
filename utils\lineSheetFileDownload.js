import ExcelJS from "exceljs";
import axios from "axios";
import fs from "fs";
import path from "path";
import sharp from "sharp";
import { uploadToS3 } from "./helperFunction.js";
import axiosRetry from "axios-retry";
import { getPricesForCustomerAndSkus } from "../helpers/priceHelper.js";
import { generateSignedURL } from "./helperFunction.js";

const __filename = decodeURI(new URL(import.meta.url).pathname).slice(1);
const __dirname = path.dirname(__filename);

const generateUniqueId = () => {
  return Math.random().toString(36).substring(2, 10);
};

axiosRetry(axios, {
  retries: 3, // Number of retries
  retryDelay: (retryCount) => {
    console.log(`Retry attempt: ${retryCount}`);
    return retryCount * 1000; // Delay between retries (1s, 2s, 3s)
  },
  retryCondition: (error) => {
    // Retry on network errors (like ECONNRESET) or if the request timeout occurs
    return (
      axiosRetry.isNetworkError(error) || axiosRetry.isRetryableError(error)
    );
  },
});

const getImageBase64 = async (imageUrl) => {
  try {
    const parsedUrl = new URL(imageUrl);
    const originalImageUrl = `${parsedUrl.origin}${parsedUrl.pathname}`;
    // Fetch the image with axios-retry handling the retries
    const response = await axios.get(originalImageUrl, {
      responseType: "arraybuffer",
      // timeout: Infinity, // Increased timeout duration
    });

    const imageBuffer = Buffer.from(response.data, "binary");

    // Resize the image using Sharp (adjust width and quality if needed)
    const resizedBuffer = await sharp(imageBuffer)
      .resize({ height: 292 }) // Use pixel height to control size
      .png({ quality: 60 })
      .toBuffer();

    // Return the resized image as a base64 string
    return resizedBuffer.toString("base64");
  } catch (error) {
    console.error(`Failed to fetch image ${imageUrl}:`, error.message);
    throw error; // Re-throw error after retry attempts
  }
};
export const generateXlsxSheet = async (
  products,
  bucketName,
  brand = "all",
  customerId, // Added customerId as a parameter
  order = false
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Images");
    const fetchedJsonData = products;
    let formattedJsonData;

    if (customerId) {
      const skus = fetchedJsonData.map((item) => item.sku);
      const newPriceData = await getPricesForCustomerAndSkus(customerId, skus);
      formattedJsonData = fetchedJsonData.map((item) => {
        const priceItem = newPriceData.find((price) => price.sku === item.sku);
        const newPrice = priceItem ? priceItem.price : item.price || 0;
        const totalStocks =
          item.sapSkus?.length > 0
            ? item.sapSkus.reduce((acc, current) => acc + current.quantity, 0)
            : 0;
        const totalPrice = newPrice * item.quantity; // Use the new price if available
        const payload = !order
          ? {
              title: item.productTitle,
              image: item.image ? item.image : "no image",
              productCode: item.sku,
              FOB: newPrice, // Use the new price here
              stock: totalStocks,
              cluster: item.cluster,
              brand: item.brand,
              collection: item.collection,
              gender: item.gender,
              caseMaterial: item.caseMaterial,
              caseWidth: item.caseWidth,
              caseLength: item.caseLength,
              caseThickness: item.caseThickness,
              strapMaterial: item.strapMaterial,
              dialColor: item.dialColor,
              pmrType: item.pmrType,
              function: item.function,
            }
          : {
              title: item.productTitle,
              image: item.image ? item.image : "no image",
              productCode: item.sku,
              FOB: newPrice, // Use the new price here
              cluster: item.cluster,
              brand: item.brand,
              collection: item.collection,
              gender: item.gender,
              caseMaterial: item.caseMaterial,
              caseWidth: item.caseWidth,
              caseLength: item.caseLength,
              caseThickness: item.caseThickness,
              strapMaterial: item.strapMaterial,
              dialColor: item.dialColor,
              pmrType: item.pmrType,
              function: item.function,
              quantity: item.quantity,
              totalValue: totalPrice,
            };
        return payload;
      });

      // Add totals row
    } else {
      // If no customerId, use the old logic without price adjustments
      formattedJsonData = fetchedJsonData.map((item) => {
        const totalStocks =
          item.sapSkus?.length > 0
            ? item.sapSkus.reduce((acc, current) => acc + current.quantity, 0)
            : 0;

        const payload = !order
          ? {
              title: item.productTitle,
              image: item.image ? item.image : "no image",
              productCode: item.sku,
              FOB: item.price || 0, // Use the new price here
              stock: totalStocks,
              cluster: item.cluster,
              brand: item.brand,
              collection: item.collection,
              gender: item.gender,
              caseMaterial: item.caseMaterial,
              caseWidth: item.caseWidth,
              caseLength: item.caseLength,
              caseThickness: item.caseThickness,
              strapMaterial: item.strapMaterial,
              dialColor: item.dialColor,
              pmrType: item.pmrType,
              function: item.function,
            }
          : {
              title: item.productTitle,
              image: item.image ? item.image : "no image",
              productCode: item.sku,
              FOB: item.price || 0, // Use the new price here
              cluster: item.cluster,
              brand: item.brand,
              collection: item.collection,
              gender: item.gender,
              caseMaterial: item.caseMaterial,
              caseWidth: item.caseWidth,
              caseLength: item.caseLength,
              caseThickness: item.caseThickness,
              strapMaterial: item.strapMaterial,
              dialColor: item.dialColor,
              pmrType: item.pmrType,
              function: item.function,
              quantity: item.quantity,
              totalValue: totalPrice,
            };
        return payload;
      });
    }

    if (!formattedJsonData.length) {
      throw new Error("Data not found");
    }

    const columns = Object.keys(formattedJsonData[0]);
    for (let i = 0; i < columns.length; i++) {
      const key = columns[i];
      const width = key === "image" ? 30 : 20; // Set width to 30 for 'image' column and 20 for others
      worksheet.getColumn(i + 1).width = width; // Set column width
      worksheet.getCell(1, i + 1).value =
        key.charAt(0).toUpperCase() + key.slice(1); // Set column header
    }

    if (order) {
      // Calculate totals
      const totalQuantity = formattedJsonData.reduce(
        (sum, item) => sum + (item.quantity || 0),
        0
      );
      const totalValue = formattedJsonData.reduce(
        (sum, item) => sum + (item.totalValue || 0),
        0
      );

      // Add totals row at the bottom
      const lastRow = formattedJsonData.length + 2;
      columns.forEach((key, index) => {
        const cell = worksheet.getCell(lastRow, index + 1);
        if (key === "quantity") {
          cell.value = totalQuantity;
        } else if (key === "totalValue") {
          cell.value = totalValue;
        } else if (key === "title") {
          cell.value = "Total";
        } else {
          cell.value = "";
        }
        cell.font = { bold: true };
      });
    }

    const batchSize = 10; // Smaller batch size for parallel image fetching
    for (let i = 0; i < formattedJsonData.length; i += batchSize) {
      const batch = formattedJsonData.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (item, batchIndex) => {
          for (const [index, key] of Object.keys(item).entries()) {
            const cell = worksheet.getCell(
              `${String.fromCharCode(65 + index)}${i + batchIndex + 2}`
            );
            if (key === "image" && item[key] !== "no image") {
              const base64Image = await getImageBase64(item[key]);
              const imageId = workbook.addImage({
                base64: base64Image,
                extension: "png",
              });
              const range = `${String.fromCharCode(65 + index)}${
                i + batchIndex + 2
              }:${String.fromCharCode(65 + index)}${i + batchIndex + 2}`;
              worksheet.addImage(imageId, range);
            } else {
              cell.value = item[key];
            }
          }
        })
      );
    }

    worksheet.getRow(1).height = 20; // Set height for heading tab
    for (let i = 2; i <= formattedJsonData.length + 1; i++) {
      worksheet.getRow(i).height = 240; // Set height for other rows
    }

    const linesheetDir = path.resolve(__dirname, "..", "constants");

    if (!fs.existsSync(linesheetDir)) {
      fs.mkdirSync(linesheetDir, { recursive: true });
    }

    const linesheetPath = path.join(linesheetDir, `line_sheet_${brand}.xlsx`);

    await workbook.xlsx.writeFile(linesheetPath);

    const uniqueId = generateUniqueId();

    const uploadResponse = await uploadToS3(
      linesheetPath,
      bucketName,
      `line_sheet_${brand}_${uniqueId}.xlsx`
    );

    // fs.unlinkSync(linesheetPath); // Delete the temporary file after streaming
    return {
      status: "success",
      downloadUrl: uploadResponse.Location,
      key: uploadResponse.Key,
    };
  } catch (error) {
    console.error("(generateXlsxSheet) Error:", error);
    throw error; // Rethrow the error for further handling if necessary
  }
};
//this runs when images are not needed in sheet
export const generateXlsxSheetCdnLinksForWebsite = async (
  products,
  customerId
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Products");

    // Formatting the data
    let formattedData = products;
    if (customerId) {
      const skus = formattedData.map((item) => item.sku);
      const newPriceData = await getPricesForCustomerAndSkus(customerId, skus);

      formattedData = formattedData.map((item) => {
        const priceItem = newPriceData.find((price) => price.sku === item.sku);
        const newPrice = priceItem ? priceItem.price : item.price || 0;
        const totalStocks =
          item.sapSkus?.length > 0
            ? item.sapSkus.reduce((acc, current) => acc + current.quantity, 0)
            : 0;

        return {
          image: item.image ? item.image : "no image",
          productCode: item.sku,
          FOB: newPrice,
          stock: totalStocks,
          cluster: item.cluster,
          gender: item.gender,
          collection: item.collection,
          brand: item.brand,
          caseMaterial: item.caseMaterial,
          caseWidth: item.caseWidth,
          caseLength: item.caseLength,
          caseThickness: item.caseThickness,
          strapMaterial: item.strapMaterial,
          dialColor: item.dialColor,
          pmrType: item.pmrType,
          function: item.function,
        };
      });
    } else {
      formattedData = formattedData.map((item) => {
        const totalStocks =
          item.sapSkus?.length > 0
            ? item.sapSkus.reduce((acc, current) => acc + current.quantity, 0)
            : 0;
        return {
          image: item.image ? item.image : "no image",
          productCode: item.sku,
          FOB: item.price || 0,
          stock: totalStocks,
          cluster: item.cluster,
          brand: item.brand,
          gender: item.gender,
          collection: item.collection,
          caseMaterial: item.caseMaterial,
          caseWidth: item.caseWidth,
          caseLength: item.caseLength,
          caseThickness: item.caseThickness,
          strapMaterial: item.strapMaterial,
          dialColor: item.dialColor,
          pmrType: item.pmrType,
          function: item.function,
        };
      });
    }

    // Adding headers
    worksheet.columns = [
      { header: "Image", key: "image", width: 30 },
      { header: "SKU CODE", key: "productCode", width: 20 },
      { header: "Brand", key: "brand", width: 20 },
      { header: "Gender", key: "gender", width: 20 },
      { header: "Cluster", key: "cluster", width: 20 },
      { header: "Collection", key: "collection", width: 20 },
      { header: "Case Material", key: "caseMaterial", width: 20 },
      { header: "Case Width (mm)", key: "caseWidth", width: 20 },
      { header: "Case Length (mm)", key: "caseLength", width: 20 },
      { header: "Case Thickness (mm)", key: "caseThickness", width: 20 },
      { header: "Strap Material", key: "strapMaterial", width: 20 },
      { header: "Dial Color", key: "dialColor", width: 20 },
      { header: "Function", key: "function", width: 20 },
      { header: "Stock", key: "stock", width: 10 },
      { header: "PMR", key: "pmrType", width: 20 },
      { header: "FOB", key: "FOB", width: 10 },
    ];

    // Adding rows
    worksheet.addRows(formattedData);

    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "A6C9EC" },
      };
      cell.font = { bold: true };
      cell.alignment = { vertical: "middle", horizontal: "center" };
    });

    worksheet.eachRow({ includeEmpty: false }, (row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.alignment = {
          vertical: "middle",
          horizontal: "center",
          wrapText: true,
        };
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });

    // Generate Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Return the buffer (to send it later in response)
    return buffer;
  } catch (error) {
    console.error("Error generating Excel sheet:", error);
    throw error;
  }
};

export const generateXlsxSheetForWebsite = async (
  products,
  bucketName,
  brand = "all",
  customerId // Added customerId as a parameter
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Images");
    const fetchedJsonData = products;

    let formattedJsonData;

    if (customerId) {
      const skus = fetchedJsonData.map((item) => item.sku);

      const newPriceData = await getPricesForCustomerAndSkus(customerId, skus);

      formattedJsonData = fetchedJsonData.map((item) => {
        const priceItem = newPriceData.find((price) => price.sku === item.sku);
        const newPrice = priceItem ? priceItem.price : item.price || 0; // Use the new price if available
        const totalStock =
          item.sapSkus?.length > 0
            ? item.sapSkus.reduce((acc, current) => acc + current.quantity, 0)
            : 0;
        return {
          title: item.productTitle,
          image: item.image ? item.image : "no image",
          productCode: item.sku,
          FOB: newPrice, // Use the new price here
          stock: totalStock,
          cluster: item.cluster,
          brand: item.brand,
          gender: item.gender,
          collection: item.collection,
          caseMaterial: item.caseMaterial,
          caseWidth: item.caseWidth,
          caseLength: item.caseLength,
          caseThickness: item.caseThickness,
          strapMaterial: item.strapMaterial,
          dialColor: item.dialColor,
          pmrType: item.pmrType,
          function: item.function,
        };
      });
    } else {
      // If no customerId, use the old logic without price adjustments
      formattedJsonData = fetchedJsonData.map((item) => ({
        title: item.productTitle,
        image: item.image ? item.image : "no image",
        productCode: item.sku,
        FOB: item.price || 0, // Use the original price here
        cluster: item.cluster,
        brand: item.brand,
        collection: item.collection,
        gender: item.gender,
        caseMaterial: item.caseMaterial,
        caseWidth: item.caseWidth,
        caseLength: item.caseLength,
        caseThickness: item.caseThickness,
        strapMaterial: item.strapMaterial,
        dialColor: item.dialColor,
        pmrType: item.pmrType,
        function: item.function,
      }));
    }

    if (!formattedJsonData.length) {
      throw new Error("Data not found");
    }

    const columns = Object.keys(formattedJsonData[0]);
    for (let i = 0; i < columns.length; i++) {
      const key = columns[i];
      const width = key === "image" ? 30 : 20; // Set width to 30 for 'image' column and 20 for others
      worksheet.getColumn(i + 1).width = width; // Set column width
      worksheet.getCell(1, i + 1).value =
        key.charAt(0).toUpperCase() + key.slice(1); // Set column header
    }

    const batchSize = 10; // Smaller batch size for parallel image fetching
    for (let i = 0; i < formattedJsonData.length; i += batchSize) {
      const batch = formattedJsonData.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (item, batchIndex) => {
          for (const [index, key] of Object.keys(item).entries()) {
            const cell = worksheet.getCell(
              `${String.fromCharCode(65 + index)}${i + batchIndex + 2}`
            );
            if (key === "image" && item[key] !== "no image") {
              const base64Image = await getImageBase64(item[key]);
              const imageId = workbook.addImage({
                base64: base64Image,
                extension: "png",
              });
              const range = `${String.fromCharCode(65 + index)}${
                i + batchIndex + 2
              }:${String.fromCharCode(65 + index)}${i + batchIndex + 2}`;
              worksheet.addImage(imageId, range);
            } else {
              cell.value = item[key];
            }
          }
        })
      );
    }

    worksheet.getRow(1).height = 20; // Set height for heading tab
    for (let i = 2; i <= formattedJsonData.length + 1; i++) {
      worksheet.getRow(i).height = 240; // Set height for other rows
    }

    const linesheetDir = path.resolve(__dirname, "..", "constants");

    if (!fs.existsSync(linesheetDir)) {
      fs.mkdirSync(linesheetDir, { recursive: true });
    }

    const linesheetPath = path.join(linesheetDir, `line_sheet_${brand}.xlsx`);

    await workbook.xlsx.writeFile(linesheetPath);

    const uploadResponse = await uploadToS3(
      linesheetPath,
      bucketName,
      `line_sheet_${brand}.xlsx`
    );

    // fs.unlinkSync(linesheetPath); // Delete the temporary file after streaming - commented out for testing
    return {
      status: "success",
      downloadUrl: uploadResponse.Location,
      key: uploadResponse.Key,
    };
  } catch (error) {
    console.error("(generateXlsxSheet) Error:", error);
    throw error; // Rethrow the error for further handling if necessary
  }
};
