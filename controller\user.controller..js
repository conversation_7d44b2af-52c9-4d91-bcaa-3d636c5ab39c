import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import User from "../model/user.model.js";
import { authenticate } from "./authenticate.controller.js";
import validator from "validator";

import {
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";

export const getUsers = getAll(User);
export const getOneUser = getOne(User);
export const createUser = async (req, res) => {
  try {
    const { name, username, password, userGroup } = req.body;

    if (typeof name !== "string" || typeof username !== "string") {
      return res.status(401).json({ error: "Invalid username or name" });
    }

    const checkUsernameExists = await User.findOne({ username: username });

    if (checkUsernameExists != null) {
      throw new Error(`${checkUsernameExists.username} already exists.`);
    }

    let hashedPassword = password;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 10);
    }

    const user = new User({
      name,
      username,
      password: hashedPassword,
      userGroup,
    });

    const savedUser = await user.save();
    delete savedUser.password;
    res.status(201).json(savedUser);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;

    if (id === "663b2c9f982735a59fcc33d1") {
      throw new Error("Can't update Super Admin");
    }

    if (req.body.password) {
      if (req.body.password.length >= 6) {
        const hashedPassword = await bcrypt.hash(req.body.password, 10);
        req.body.password = hashedPassword;
      } else {
        return res
          .status(400)
          .json({ error: "Password (at least 6 characters)" });
      }
    }

    if (req.body.userGroup && req.body.userGroup.length === 0) {
      return res.status(400).json({ error: "Select at least one role" });
    }

    if (Object.keys(req.body).includes("name") && req.body.name.length < 3) {
      return res
        .status(400)
        .json({ error: "Name should be of at least 3 characters" });
    }

    if (
      Object.keys(req.body).includes("username") &&
      req.body.username.length < 6
    ) {
      return res
        .status(400)
        .json({ error: "Username should be of at least 6 characters" });
    }

    const updatedUser = await User.findByIdAndUpdate(id, req.body, {
      new: true,
    });

    if (!updatedUser) {
      return res.status(404).json({ error: "User not found" });
    }
    delete updatedUser.password;
    res.json(updatedUser);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    if (id === "663b2c9f982735a59fcc33d1") {
      throw new Error("Can't delete Super Admin");
    }

    const deletedUser = await User.findByIdAndDelete(id);

    if (!deletedUser) {
      return res.status(404).json({ error: "User not found" });
    }

    res.status(200).json({ success: "User Deleted Successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Controller function for user login
export const loginUser = async (req, res) => {
  const { username, password } = req.body;
  try {
    if (typeof username !== "string") {
      return res.status(401).json({ error: "Invalid username or password" });
    }

    if (!validator.isEmail(username)) {
      return res.status(401).json({ error: "Invalid username provided" });
    }

    const user = await authenticate(username, password);
    if (!user) {
      return res.status(401).json({ error: "Invalid username or password" });
    }

    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username,
        name: user.name,
        type: user.type,
      },
      process.env.JWT_TOKEN_SECRET,
      { expiresIn: "8h" }
    );

    return res.json({ token, user });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Internal server error" });
  }
};
