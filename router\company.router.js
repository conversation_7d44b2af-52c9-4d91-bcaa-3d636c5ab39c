import express from "express";
import {
  getCompanies,
  createCompany,
  getOneCompany,
  updateCompany,
  deleteCompany,
  updateCompanyPriority,
  updateCompanyShopify,
} from "../controller/company.controller.js";
import { createCompanyShopify } from "../controller/company.controller.js";
import { authenticateAdminPeopleAccess } from "../middlewares/authenticateDepttPeople.js";

const router = express.Router();

router
  .route("/")
  .get(authenticateAdminPeopleAccess("company", "read"), getCompanies)
  .post(
    authenticateAdminPeopleAccess("company", "write"),
    createCompanyShopify,
    createCompany
  );

router
  .route("/priority/update")
  .patch(
    authenticateAdminPeopleAccess("company", "write"),
    updateCompanyPriority
  );

router
  .route("/:id")
  .get(authenticateAdminPeopleAccess("company", "read"), getOneCompany)
  .patch(
    authenticateAdminPeopleAccess("company", "write"),
    updateCompanyShopify,
    updateCompany
  )
  .delete(authenticateAdminPeopleAccess("company", "delete"), deleteCompany);

export default router;
