import Shipment from "../../model/shipment.model.js";
import Order from "../../model/order.model.js";
import Inventory from "../../model/inventory.model.js";
import Company from "../../model/company.model.js";
import BulkProcessingStatus from "../../model/bulkProcessing.model.js";
import { getDepartmentForCreated } from "../../utils/findNotifiedDepartment.js";
import { createShipments } from "../../controller/action.controller.js";
import Address from "../../model/address.model.js";
import Status from "../../model/status.model.js";
import Distributor from "../../model/distributor.model.js";
import mongoose from "mongoose";

mongoose
  .connect(`${process.env.MONGO_DB_URL}`)
  .then(async (con) => {
    console.log("DB connection successfull");
  })
  .catch((error) => {
    console.log(error.name, error.message);
  });

process.on(
  "message",
  async ({
    alignedOrders,
    alignedShipmentStatus,
    nonAlignedDepartmentPeoples,
    alignedDepartmentPeoples,
  }) => {
    for (let i = 0; i < alignedOrders.length; i++) {
      try {
        const order = alignedOrders[i];
        const distributor = alignedOrders[i].distributor;
        const shipments = await createShipments(order);
        if (shipments.inventoryToUpdate.length) {
          const bulkUpdateOps = shipments.inventoryToUpdate.map((x) => ({
            updateOne: {
              filter: { "sapSkus._id": x._id, "sapSkus.sku": x.sku }, // Match the sapSkus array element by SKU
              update: {
                $set: {
                  "sapSkus.$[element].quantity": x.quantity, // Update the quantity of the matching sapSku
                },
                $inc: {
                  "sapSkus.$[element].onHold": x.on_hold, // Increment the on_hold value of the matching sapSku
                },
              },
              arrayFilters: [
                { "element.sku": x.sku }, // Ensure the update targets the correct sapSku
              ],
            },
          }));

          const bulkWriteResult = await Inventory.bulkWrite(bulkUpdateOps);
        }
        const allShipmentNames = [];
        let shipment_number = 1;
        if (shipments.alignedShipment.length) {
          const aligned = {
            name: order.name + `_${shipment_number}`,
            order: order._id,
            order_data: {
              _id: order._id,
              name: order.name,
            },
            distributor_data: {
              name: distributor.name,
              _id: distributor._id.toString(),
              email: distributor.email,
              country: distributor.country,
            },
            sapSkusUsed: [shipments.alignedShipment.sapSkusUsed],
            status: "663c4b27864c679bd21974ac", //TODO this need to be dynamic
            status_data: {
              name: alignedShipmentStatus.status,
            },
            amount: shipments.alignedShipment.reduce((acc, lineItem) => {
              return acc + lineItem.requested * lineItem.price;
            }, 0),
            shipping_address: order.shipping_address,
            lineItems: shipments.alignedShipment,
            timeline: [
              {
                time: new Date(),
                comment: `Aligned shipment created.`,
              },
            ],
            status_change_history: [
              {
                status: "663c4b27864c679bd21974ac", //TODO this need to be dynamic
              },
            ],
            initial_status: "aligned",
          };
          const createdAlignedShipment = await new Shipment(aligned).save();
          allShipmentNames.push(aligned.name);
          shipment_number++;

          const alignedDepartmentNotified = await getDepartmentForCreated(
            alignedShipmentStatus._id
          );
          const company = await Company.findOne({
            shopifyCompanyId: distributor.shopifyCompanyId,
          });
          const recipients = alignedDepartmentPeoples.map(
            (departmentPeople) => ({
              name: departmentPeople.name,
              email: departmentPeople.email,
              cc: Array.isArray(alignedDepartmentNotified)
                ? [...alignedDepartmentNotified]
                : [],
            })
          );
        }
        if (shipments.nonAlignedShipment.length) {
          const nonAligned = {
            name: order.name + `_${shipment_number}`,
            order: order._id,
            order_data: {
              _id: order._id,
              name: order.name,
            },
            distributor_data: {
              name: distributor.name,
              _id: distributor._id.toString(),
              email: distributor.email,
              country: distributor.country,
            },
            status_data: {
              name: alignedShipmentStatus.status,
            },
            status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
            amount: shipments.nonAlignedShipment.reduce((acc, lineItem) => {
              return acc + lineItem.requested * lineItem.price;
            }, 0),
            shipping_address: order.shipping_address,
            lineItems: shipments.nonAlignedShipment,
            timeline: [
              {
                time: new Date(),
                comment: `Non-Aligned shipment created.`,
              },
            ],
            status_change_history: [
              {
                status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
              },
            ],
            initial_status: "non-aligned",
          };
          const createdNonAlignedShipment = await new Shipment(
            nonAligned
          ).save();

          const nonAlignedDepartmentNotified = await getDepartmentForCreated(
            alignedShipmentStatus._id
          );

          const company = await Company.findOne({
            shopifyCompanyId: distributor.shopifyCompanyId,
          });
          const recipients = nonAlignedDepartmentPeoples.map(
            (departmentPeople) => ({
              name: departmentPeople.name,
              email: departmentPeople.email,
              cc: Array.isArray(nonAlignedDepartmentNotified)
                ? [...nonAlignedDepartmentNotified]
                : [],
            })
          );
        }
        const valuesToPush = allShipmentNames.map((x) => ({
          time: new Date(),
          comment: `Shipment ${x} created.`,
        }));

        await Order.findByIdAndUpdate(order._id, {
          $set: {
            status: "664372f129f6db844ee8bc0b", //this need to be dynamic
          },
          $push: { timeline: { $each: valuesToPush } },
        });

        const isCompleted =
          ((i + 1) / alignedOrders.length) * 100 === 100
            ? "COMPLETED"
            : "RUNNING";

        await BulkProcessingStatus.findOneAndUpdate(
          { type: "Order Process" },
          {
            $set: {
              completion: Math.floor(((i + 1) / alignedOrders.length) * 100),
              status: isCompleted,
              isProcessRunning: isCompleted !== "COMPLETED",
              errorMessage: "",
              error: false,
            },
          },
          { new: true }
        );
      } catch (err) {
        console.log(" Error in processing the orders", err);
        await BulkProcessingStatus.findOneAndUpdate(
          { type: "Order Process" },
          {
            $set: {
              error: true,
              isProcessRunning: false,
              errorMessage: err.message,
              status: "FAILED",
            },
          },
          {
            new: true,
          }
        );
        process.send({ error: err.message, status: "failed" });
      }
    }

    process.send({ status: "completed", error: null });
  }
);
