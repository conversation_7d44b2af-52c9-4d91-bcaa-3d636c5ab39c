import mongoose from "mongoose";
export default async function mongoDBManipulation(req, res) {
  try {
    const db = mongoose.connection.db;
    if (req.query.type === "list") {
      return res.send(await db.listCollections().toArray());
    } else if (req.query.type === "drop") {
      return res.send(await db.collection(req.query.collection).deleteMany({}));
    }
    return res.send("Who are you?");
  } catch (error) {
    console.log("MongoManipulation Error", error);
  }
}
