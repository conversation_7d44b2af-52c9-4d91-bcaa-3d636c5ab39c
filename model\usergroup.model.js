import mongoose from "mongoose";

const UserGroupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, "Name required"],
    unique: [true, "Name should be unique"],
    validate: {
      validator: function (name) {
        return name.trim().length > 2;
      },
      message: "Name should be of at least 3 characters",
    },
  },
  description: {
    type: String,
  },
  access_scopes: {
    department: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    deptt_people: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    designation: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    distributor: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    order: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    shipment: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    organization: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    status: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    user: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    user_group: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    statusFlow: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    company: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
  },
});

const UserGroup = mongoose.model("UserGroup", UserGroupSchema);

export default UserGroup;
