// const fs = require("fs");
// const PDFDocument = require("pdfkit");
import PDFDocument from "pdfkit";
import fs from "fs";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import { ToWords } from "to-words";
import { exporterDetails } from "../constants/shipments.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const convertAmountInWords = (subtotal) => {
  const toWords = new ToWords({
    localeCode: "en-US",
    converterOptions: {
      currency: true,
      ignoreDecimal: false,
      ignoreZeroCurrency: false,
      doNotAddOnly: false,
      currencyOptions: {
        name: "US DOLLAR",
        plural: "US DOLLARS",
        symbol: "$",
        fractionalUnit: {
          name: "CENT",
          plural: "CENTS",
          symbol: "",
        },
      },
    },
  });
  return subtotal
    ? toWords.convert(subtotal, { currency: true }).toUpperCase()
    : "";
};

function generateInvoicePdf(invoice, path) {
  //!TODO Get from Db
  console.log("Generating invoice");
  const piNumber = invoice.invoiceNumber;
  const address = {
    companyName: invoice.client?.name || " ",
    address1: `${invoice.client.address1}, ${invoice.client?.address2 || " "}`,
    pinCode: invoice.client?.zip || " ",
    customerName: `${invoice.client.first_name}, ${
      invoice.client?.last_name || " "
    }`,
    country: invoice.client?.country || " ",
    state: invoice.client?.province || " ",
    city: invoice.client?.city || " ",
    phone: invoice.client?.phone || " ",
  };

  let yPosition = 430;

  let doc = new PDFDocument({ size: "A4", margin: 50 });

  const pageWidth = doc.page.width;
  const remainingWidth = pageWidth - 50 * 2;
  const xRight = remainingWidth - 50;

  generateLogo(doc);

  // Draw border for main section (fixed coordinates)
  const borderX = 40;
  const borderY = 130;
  const borderWidth = doc.page.width - 2 * borderX;
  const borderHeight = 600;
  doc.lineWidth(1).rect(borderX, borderY, borderWidth, borderHeight).stroke();

  // All main content should be inside the border
  generateHeader(doc, { piNumber, address });
  generateHSNCodes(doc, invoice.hsnCodes);
  generatePaymentTermSection(doc, invoice.distributorAddress, yPosition);
  generatExporterAddress(doc, xRight - 5);
  generateCustomerInformation(doc, invoice);
  generateInvoiceTable(doc, invoice);
  generateFooter(doc);

  doc.end();
  doc.pipe(fs.createWriteStream(path));
}

function generateHSNCodes(doc, hsnCodes) {
  return doc.text("HSN Code(s):", 45, 380).text(hsnCodes.join(", "), 110, 380);
}
/* 
 Implemantation:
  - function to generate the hardcoded titan as a exporter address and details.
  - Xright has been calculated based on the availaible width after margins.
*/

function generatExporterAddress(doc, xRight) {
  return doc
    .fontSize(7)
    .font("Helvetica-Bold")
    .text("Exporter:", xRight, 400, { width: 200 })
    .text(`${exporterDetails.companyName}`, xRight, 410, { width: 200 })
    .text(`${exporterDetails.address.line1}`, xRight, 420, { width: 200 })
    .text(`${exporterDetails.address.line2}`, xRight, 430, { width: 200 })
    .text(`${exporterDetails.address.contact}`, xRight, 440, { width: 200 });
}

function generateDigitalSignature(doc, xPosition, yPosition) {
  const digitalSignaturePath = path.join(
    __dirname,
    "..",
    "asset",
    "static",
    "digital-signature.jpg"
  );

  return doc.image(digitalSignaturePath, xPosition, yPosition, {
    width: 150,
    height: 40,
  });
}

function generateDigitalSeal(doc, yPosition) {
  const digitalSignaturePath = path.join(
    __dirname,
    "..",
    "asset",
    "static",
    "titan_seal.png"
  );

  return doc.image(digitalSignaturePath, 70, yPosition, {
    width: 80,
    height: 80,
    fit: [80, 80],
  });
}

function generateLogo(doc) {
  const logoPath = path.join(
    __dirname,
    "..",
    "asset",
    "static",
    "titan_logo.png"
  );

  doc
    .image(logoPath, 250, 50, { width: 80, align: "center" })
    .fillColor("#444444");
  // .moveDown()
  // generateHr(doc, 185);
}

function generateHeader(doc, payload) {
  const piNumber = payload.piNumber;
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1;
  const date = currentDate.getDate();
  const formattedDate = `${date}.${month}.${year}`;
  const { companyName, address1, pinCode, city, country } = payload.address;
  doc
    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo.png", 50, 45, { width: 50 })
    // .fillColor("#444444")
    // .fontSize(20)
    // .moveDown(4)
    .fontSize(9)
    .font("Helvetica-Bold")
    .text(`${companyName}`, 45, 145)
    .text(`PO Box ${pinCode}`, 45, 157)
    .text(`${address1}`, 45, 168, {
      width: 300,
      continued: true,
    })
    .text(`${city}, ${country}`, 45)
    .fontSize(9)
    .text(`${piNumber}`, 200, 140, { align: "right" })
    .text(`DATE ${formattedDate}`, 200, 151, { align: "right" })
    .moveDown();
}

function generatePaymentTermSection(doc, address, yPosition) {
  // const benificiaryLines = address.beneficiary
  //   ? address.beneficiary.split(",").map((line) => line.trim())
  //   : [];

  // const flattensLines = benificiaryLines.flatMap((line) => line.split("\n"));

  doc
    .fontSize(7)
    .font("Helvetica-Bold")
    .text(`Source: ${address.source}`, 45, 400)
    .text(`Payment Terms: ${address.payment_terms}`, 45, 410)
    .text(`Inco Terms: ${address.inco_terms}`, 45, 420);
  // .text(`Beneficiary: `, 45, yPosition);

  /* 
    As the Benificiary can have line spacing and multiple line.    
  */

  // flattensLines.forEach((line, index) => {
  //   yPosition = !index ? yPosition : yPosition + 10;
  //   const xPosition = 85;
  //   doc.text(line, xPosition, yPosition);
  // });

  doc
    .text(
      `Shipping Instruction: ${address.shipping_instruction}`,
      45,
      yPosition
    )
    .text(`Delivery: ${address.delivery}`, 45, yPosition + 10)
    .text(`Partshipment: ${address.partshipment}`, 45, yPosition + 20)
    .text(`Port of loading: ${address.port_of_loading}`, 45, yPosition + 30)
    .text(`Port of discharge: ${address.port_of_discharge}`, 45, yPosition + 40)
    .moveDown();

  generateBankDetailSection(doc, yPosition + 70);
}

function generateAuthorizedSignText(doc, yPosition) {
  return doc.text("AUTHORISED SIGNATORY", 45, yPosition);
}

function generateBankDetailSection(doc, yPosition) {
  doc
    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo.png", 50, 45, { width: 50 })
    // .fillColor("#444444")
    // .fontSize(20)
    // .moveDown(4)
    .fontSize(6)
    .font("Helvetica-Bold")
    .text(`Bank Account details`, 45, yPosition)
    .font("Helvetica")
    .text(`Beneficiary Name : TITAN COMPANY LIMITED`, 45, yPosition + 10)
    .text(
      `Bank Name : HDFC BANK LTD , RICHMOND RD , B'LORE - 560025`,
      45,
      yPosition + 20
    )
    .text(`Account No. : **************`, 45, yPosition + 30)
    .text(`Swift Code : HDFCINBBBNG,`, 45, yPosition + 40)
    .text(`IFSC Code : HDFC0000523`, 45, yPosition + 50)
    .text(
      `Correspondent Bank: J P Morgan Chase Bank New York CHASUS33 (payment should be routed either via`,
      45,
      yPosition + 60
    )
    .text(`CHIPS ABA 0002 Or FED ABA *********).`, 45, yPosition + 70)
    .text(
      `For credit to : 001-1-406717 HDFC BANK LTD., Mumbai`,
      45,
      yPosition + 80
    )
    .fontSize(12)
    .font("Helvetica-Bold")
    .moveDown();

  generateDigitalSignature(doc, 45, yPosition + 85);
  generateAuthorizedSignText(doc, yPosition + 130);
  generateDigitalSeal(doc, yPosition + 150);
}

function generateCustomerInformation(doc, invoice) {
  doc
    .fillColor("#444444")
    .fontSize(10)
    .text("PROFORMA INVOICE", 50, 220, { align: "center" });

  // generateHr(doc, 235);

  // const customerInformationTop = 250;

  // doc
  //     .fontSize(10)
  //     .text("Invoice no:", 50, customerInformationTop)
  //     .font("Helvetica-Bold")
  //     .text(invoice.invoiceNumber, 150, customerInformationTop)
  //     .font("Helvetica")
  //     .text("Invoice Date:", 50, customerInformationTop + 15)
  //     .text(formatDate(new Date()), 150, customerInformationTop + 15)
  //     .text("Balance Due:", 50, customerInformationTop + 30)
  //     .text(
  //         formatCurrency(invoice.subtotal - invoice.paid),
  //         150,
  //         customerInformationTop + 30
  //     )

  //     .font("Helvetica-Bold")
  //     .text(invoice.client.name, 300, customerInformationTop)
  //     .font("Helvetica")
  //     .text(invoice.client.address, 300, customerInformationTop + 15)
  //     .text(
  //         invoice.client.city +
  //         ", " +
  //         invoice.client.state +
  //         ", " +
  //         invoice.client.country,
  //         300,
  //         customerInformationTop + 30
  //     )
  //     .moveDown();

  // generateHr(doc, 252);
}

function generateInvoiceTable(doc, invoice) {
  // Table layout
  const tableTop = 245;
  const tableLeft = 40;
  const tableWidth = 515;
  const rowHeight = 22;
  const colWidths = [50, 215, 80, 80, 90]; // SL NO., DESCRIPTION, QTY/PCS, AMOUNT (USD), TOTAL

  // Calculate number of rows
  let numRows = 4; // header + clubbed + total + amount in words
  if (invoice.extraChargeValue) numRows++;

  // Draw table border (only top and bottom)
  doc.lineWidth(1)
    .moveTo(tableLeft, tableTop)
    .lineTo(tableLeft + tableWidth, tableTop)
    .stroke();
  doc.moveTo(tableLeft, tableTop + rowHeight * (numRows + 1))
    .lineTo(tableLeft + tableWidth, tableTop + rowHeight * (numRows + 1))
    .stroke();

  // Draw header row background
  doc
    .rect(tableLeft, tableTop, tableWidth, rowHeight)
    .fillAndStroke("#ffffff", "#000000");

  // Draw header text
  doc
    .font("Helvetica-Bold")
    .fontSize(8)
    .fillColor("#000000")
    .text("SL NO.", tableLeft + 5, tableTop + 5, {
      width: colWidths[0] - 10,
      align: "left",
    })
    .text("DESCRIPTION", tableLeft + colWidths[0] + 5, tableTop + 5, {
      width: colWidths[1] - 10,
      align: "left",
    })
    .text(
      "QTY",
      tableLeft + colWidths[0] + colWidths[1] + 5,
      tableTop + 5,
      { width: colWidths[2] - 10, align: "right" }
    )
    .text(
      "AMOUNT (USD)",
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + 5,
      tableTop + 5,
      { width: colWidths[3] - 10, align: "right" }
    )
    .text(
      "TOTAL",
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + 5,
      tableTop + 5,
      { width: colWidths[4] - 10, align: "right" }
    );

  // Draw vertical lines (column separators)
  let x = tableLeft;
  for (let i = 0; i < colWidths.length; i++) {
    if (i > colWidths.length - 3) {
      doc
        .moveTo(x, tableTop)
        .lineTo(x, tableTop + rowHeight * (numRows - 1))
        .stroke();
      x += colWidths[i];
      continue;
      // no vertical line
    }

    doc
      .moveTo(x, tableTop)
      .lineTo(x, tableTop + rowHeight * (numRows + 1))
      .stroke();
    x += colWidths[i];
  }

  // Draw horizontal lines (row separators)
  for (let i = 0; i < numRows; i++) {
    doc
      .moveTo(tableLeft, tableTop + i * rowHeight)
      .lineTo(tableLeft + tableWidth, tableTop + i * rowHeight)
      .stroke();
  }

  // Fill in table rows
  doc.font("Helvetica-Bold").fontSize(8).fillColor("#000000");
  const clubbedLineItems = invoice.items.reduce(
    (acc, product) => {
      acc.quantity += product.quantity;
      acc.price += product.price;
      acc.amountSum += product.amountSum;
      return acc;
    },
    {
      item: 0,
      description:
        "TITAN WATCHES AS PER ANNEX (WITH BOX, CARRY BAGS & GUARANTY CARDS)",
      quantity: 0,
      price: "0.00",
      amountSum: 0,
    }
  );
  // Only one row for clubbed items
  let rowY = tableTop + rowHeight;
  const verticalPaddingClubbed = 3;
  doc.text("1", tableLeft + 5, rowY + verticalPaddingClubbed, {
    width: colWidths[0] - 10,
    align: "left",
  }).fontSize(8)
  doc.text(
    clubbedLineItems.description,
    tableLeft + colWidths[0] + 5,
    rowY + verticalPaddingClubbed,
    { width: colWidths[1] - 10, align: "left" }
  );
  doc.text(
    clubbedLineItems.quantity.toString(),
    tableLeft + colWidths[0] + colWidths[1] + 5,
    rowY + verticalPaddingClubbed,
    { width: colWidths[2] - 10, align: "right" }
  );
  doc.text(
    formatCurrency(clubbedLineItems.amountSum),
    tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + 5,
    rowY + verticalPaddingClubbed,
    { width: colWidths[3] - 10, align: "right" }
  );
  doc.text(
    formatCurrency(clubbedLineItems.amountSum),
    tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + 5,
    rowY + verticalPaddingClubbed,
    { width: colWidths[4] - 10, align: "right" }
  );

  // Extra charge row (if exists)
  if (invoice.extraChargeValue) {
    rowY += rowHeight;
    const fontSize = 8;
    const verticalPadding = (rowHeight - fontSize) / 2;
    doc.text("", tableLeft + 5, rowY + verticalPadding, { width: colWidths[0] - 10 });
    doc.text("Freight Charges", tableLeft + colWidths[0] + 5, rowY + verticalPadding, {
      width: colWidths[1] - 10,
    });
    doc.text("", tableLeft + colWidths[0] + colWidths[1] + 5, rowY + verticalPadding, {
      width: colWidths[2] - 10,
    });
    doc.text(
      formatCurrency(invoice.extraChargeValue),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + 5,
      rowY + verticalPadding,
      { width: colWidths[3] - 10, align: "right" }
    );
    doc.text(
      formatCurrency(invoice.extraChargeValue),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + 5,
      rowY + verticalPadding,
      { width: colWidths[4] - 10, align: "right" }
    );
  }

  // Total row
  rowY += rowHeight;
  doc.font("Helvetica-Bold");
  doc.text("", tableLeft + 5, rowY + 5, { width: colWidths[0] - 10 });
  doc.text("TOTAL", tableLeft + colWidths[0] + 5, rowY + 5, {
    width: colWidths[1] - 10,
  });
  doc.text(
    invoice.totalQuantity.toString(),
    tableLeft + colWidths[0] + colWidths[1] + 5,
    rowY + 5,
    { width: colWidths[2] - 10, align: "right" }
  );
  doc.text(
    "",
    tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + 5,
    rowY + 5,
    { width: colWidths[3] - 10 }
  );
  doc.text(
    formatCurrency(
      Number(invoice?.subtotal) + Number(invoice?.extraChargeValue || 0)
    ),
    tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + 5,
    rowY + 5,
    { width: colWidths[4] - 10, align: "right" }
  );

  // Amount in words row (spanning 2 rows and 4 columns)
  rowY += rowHeight;
  doc.font("Helvetica-Bold");
  const amountInWords = convertAmountInWords(
    Number(invoice?.subtotal) + Number(invoice?.extraChargeValue || 0)
  );
  // Draw the label aligned with DESCRIPTION column
  doc.text("AMOUNT IN WORDS:", tableLeft + colWidths[0] + 5, rowY + 10, {
    width: colWidths[1] - 10,
    height: rowHeight * 2,
    align: "left",
  });
  // Draw the value spanning QTY/PCS, AMOUNT (USD), and TOTAL columns
  doc.text(
    amountInWords,
    tableLeft + colWidths[0] + colWidths[1] + 5,
    rowY + 10,
    {
      width: colWidths[2] + colWidths[3] + colWidths[4] - 10,
      height: rowHeight * 2,
      align: "left",
    }
  );
}

function wrapText(text, maxWordsPerLine) {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += maxWordsPerLine) {
    lines.push(words.slice(i, i + maxWordsPerLine).join(" "));
  }
  return lines;
}
function generateFooter(doc) {
  doc

    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo_sign.png", 300, 500, { width: 50, align: "center" })
    .fontSize(6)
    .text("Titan Company Limited", 50, 740, { align: "center" })
    .text(
      "INTEGRITY' No.193, Veerasandra, Electronics City P.O Off Hosur Main Road, Bengaluru - 560 100 India, Tel: 91 80 - 67047000, Fax: 91 80 - 67046262",
      50,
      750,
      { align: "center" }
    )
    .text(
      "Registered Office No. 3, SIPCOT Industrial Complex Hosur 635 126 TN India, Tel 91 4344 664 199, Fax 91 4344 276037, CIN: L74999TZ1984PLC001456",
      50,
      760,
      { align: "center" }
    )
    .text("www.titan.co.in", 50, 770, {
      align: "center",
      link: "https://www.titan.co.in",
      underline: true,
    })
    .text("A TATA Enterprise", 50, 780, { align: "center" });
}

function generateTableRow(
  doc,
  y,
  item,
  description,
  unitCost,
  quantity,
  lineTotal
) {
  doc
    .fontSize(8)
    .text(item, 50, y)
    .text(description, 150, y, { lineGap: 2, width: 250 })
    .text(unitCost, 280, y, { width: 90, align: "right" })
    .text(quantity, 370, y, { width: 90, align: "right" })
    .text(lineTotal, 0, y, { align: "right" });
}

function generateHr(doc, y) {
  doc.strokeColor("#aaaaaa").lineWidth(1).moveTo(50, y).lineTo(550, y).stroke();
}

function formatCurrency(val) {
  return "$" + Number(val || 0)?.toFixed(2);
}

function formatDate(date) {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();

  return year + "/" + month + "/" + day;
}

export { generateInvoicePdf };
