import express from "express";
import {
  getOneOrder,
  updateOrder,
  deleteOrder,
  getOrderByStatus,
  convertCartToOrder,
  generateOrderSheet,
  editOrderFromSheet,
  getCountryManagerOrders,
} from "../controller/order.controller.js";
import { notifyStatusChange } from "../controller/common/index.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";
import authenticateDepttPeopleAccess from "../middlewares/authenticateDepttPeople.js";
const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

router
  .route("/")
  .get(authenticateDepttPeopleAccess("order", "read"), getCountryManagerOrders);

router
  .route("/order-by-status")
  .get(authenticateDepttPeopleAccess("order", "read"), getOrderByStatus);

router
  .route("/cart/:cartId")
  .post(authenticateDepttPeopleAccess("order", "write"), convertCartToOrder);

router
  .route("/:id/generate_sheet")
  .get(authenticateDepttPeopleAccess("order", "read"), generateOrderSheet);
router
  .route("/:id/edit_sheet")
  .post(
    authenticateDepttPeopleAccess("order", "write"),
    upload.single("file"),
    editOrderFromSheet
  );

router
  .route("/:id")
  .get(authenticateDepttPeopleAccess("order", "read"), getOneOrder)
  .patch(
    authenticateDepttPeopleAccess("order", "write"),
    notifyStatusChange,
    updateOrder
  )
  .delete(authenticateDepttPeopleAccess("order", "delete"), deleteOrder);

export default router;
