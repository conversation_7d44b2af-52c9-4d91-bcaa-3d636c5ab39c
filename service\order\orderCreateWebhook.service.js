import axios from "axios";

export async function orderCreateWebhook() {
  await axios
    .request({
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `mutation MyMutation {
        webhookSubscriptionCreate(
          webhookSubscription: {callbackUrl: "${process.env.APP_URL}/api/storeOrderInDB", format: JSON}
          topic: ORDERS_CREATE
        ) {
          userErrors {
            field
            message
          }
          webhookSubscription {
            id
            updatedAt
            includeFields
            createdAt
            callbackUrl
          }
        }
      }`,
        variables: {},
      }),
    })
    .then((response) => {
      return true;
    })
    .catch((error) => {
        console.log(error, " errorrieving webhook subscription")
      return error;
    });
}