import mongoose from "mongoose";

const bulkProcessingStatusSchema = new mongoose.Schema({
  type: {
    type: String,
    default: "Order Process",
  },
  status: {
    type: String,
  },
  error: {
    type: Boolean,
    default: false,
  },
  errorMessage: {
    type: String,
  },
  completion: {
    type: Number,
    default: 0,
  },
});

export default mongoose.models.BulkProcessingStatus ||
  mongoose.model("BulkProcessingStatus", bulkProcessingStatusSchema);
