import EmailNotification from "../../model/emailNotification.model.js";
import { Email } from "../rule/index.js";
import Shipment from "../../model/shipment.model.js";
import Status from "../../model/status.model.js";
import Department from "../../model/department.model.js";
import { generateXlsxSheet } from "../../utils/lineSheetFileDownload.js";
import { getDepartmentForCreated } from "../../utils/findNotifiedDepartment.js";
import Distributor from "../../model/distributor.model.js";

export const emailSendCron = async () => {
  const emailNotifications = await EmailNotification.find({
    isProcessed: false,
  }).limit(10);

  for (const emailNotification of emailNotifications) {
    if (
      emailNotification.emailPayload.pseudoId !== "DELIVERY_CREATION_AGAINST_SO"
    ) {
      await EmailNotification.updateOne(
        { _id: emailNotification._id },
        { isProcessed: true }
      );
    }

    if (!emailNotification.reciepient || !emailNotification.emailPayload) {
      await EmailNotification.updateOne(
        { _id: emailNotification._id },
        {
          note: "Missing reciepients or email payload",
          tag: "MROP",
          isProcessed: false,
        }
      );
    }
    try {
      if (emailNotification.emailType == "ORDER_CREATE") {
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "all",
          emailNotification?.emailPayload.customerId,
          true
        );
        if (emailNotification.emailPayload.cartProducts.length < 500) {
          emailNotification.emailPayload.attachments = [link.downloadUrl];
        } else {
          emailNotification.emailPayload.link = link.downloadUrl;
        }

        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.cc,
          emailNotification?.emailCategory
        ).sendOrderCreationEmail();
      }
      if (emailNotification.emailType == "SHIPMENT_CREATE") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendShipmentCreationEmail();
      }
      if (emailNotification.emailType == "SHIPMENT_STATUS_CHANGE") {
        if (
          emailNotification.emailPayload.pseudoId ===
          "DELIVERY_CREATION_AGAINST_SO"
        ) {
          const emailCreationDate = new Date(emailNotification.createdAt);
          const futureDate = emailCreationDate.setDate(
            emailCreationDate.getDate() + 3
          );

          const currentDate = new Date();

          if (currentDate >= futureDate) {
            await new Email(
              emailNotification?.reciepient,
              emailNotification?.emailPayload,
              emailNotification?.cc,
              emailNotification?.emailCategory,
              emailNotification?.targetPugFile
            ).sendShipmentStatusChangeEmail();

            await EmailNotification.updateOne(
              { _id: emailNotification._id },
              { isProcessed: true }
            );
          }
        } else {
          await new Email(
            emailNotification?.reciepient,
            emailNotification?.emailPayload,
            emailNotification?.cc,
            emailNotification?.emailCategory,
            emailNotification?.targetPugFile
          ).sendShipmentStatusChangeEmail();
        }
      }
      if (emailNotification.emailType == "SHIPMENT_ESCALATION") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.cc,
          emailNotification?.emailCategory
        ).sendShipmentEscalationEmail();
      }
      if (emailNotification.emailType == "CUSTOMER_ACCOUNT_ACTIVATION") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendDistributorAccoutActivationEmail();
      }
      if (emailNotification.emailType == "PI_GENERATED") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendPiGenerationEmail();
      }
      if (emailNotification.emailType == "ORDER_CREATE_NOTIFY_MANAGER") {
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "all",
          emailNotification?.emailPayload.customerId,
          true
        );

        if (emailNotification.emailPayload.cartProducts.length < 500) {
          emailNotification.emailPayload.attachments = [link.downloadUrl];
        } else {
          emailNotification.emailPayload.link = link.downloadUrl;
        }

        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.cc,
          emailNotification?.emailCategory
        ).sendOrderCreateNotifyManagersEmail();
      }
      if (emailNotification.emailType == "SLA_BREACH") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendSlaBreachEmail();
      }

      if (emailNotification.emailType == "LINE_SHEET") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.cc,
          emailNotification?.emailCategory
        ).sendLineSheetEmail();
      }
    } catch (error) {
      console.log("error while sending email", error);
      await EmailNotification.updateOne(
        { _id: emailNotification._id },
        { isProcessed: false }
      );
    }
  }
};

export const generateEscalationNotification = async () => {
  try {
    const shipments = await Shipment.find({});
    for (const shipment of shipments) {
      if (
        shipment.status_change_history &&
        shipment.status_change_history.length > 0
      ) {
        const latestStatusChange = shipment.status_change_history.sort(
          (a, b) => {
            return new Date(b.createdAt) - new Date(a.createdAt);
          }
        )[0];

        const statusDetails = await Status.findById(latestStatusChange.status);
        if (statusDetails && statusDetails.escalationTriggerPeriod) {
          const escalationPeriodEndDate = new Date(
            latestStatusChange.createdAt
          );
          escalationPeriodEndDate.setDate(
            escalationPeriodEndDate.getDate() +
              statusDetails.escalationTriggerPeriod
          );

          const isOverEscalationPeriod = new Date() > escalationPeriodEndDate;

          if (isOverEscalationPeriod) {
            const escalationDepartment = statusDetails.escalationDepartment;

            // const departmentPeoples = await Department.find({
            //   _id: escalationDepartment._id,
            // });

            const departmentPeoples = await Department.find({
              departmentType: escalationDepartment._id,
            });

            const departmentDetails = await getDepartmentForCreated(
              statusDetails._id
            );

            if (shipment.escalation_status === false) {
              shipment.escalation_status = true;
              shipment.escalation_history.push({
                status: statusDetails._id,
                start: new Date(),
                end: null,
              });
              await Shipment.updateOne(
                { _id: shipment._id }, // Match the shipment by ID
                {
                  $set: { escalation_status: true },
                  $push: {
                    escalation_history: {
                      status: statusDetails._id,
                      start: new Date(),
                      end: null,
                    },
                  },
                }
              );
            }

            const distributerId = shipment.order.distributor?._id;
            const distributor = await Distributor.findOne({
              _id: distributerId,
            });

            const recipients = departmentPeoples.map((departmentPeople) => ({
              name: departmentPeople.name,
              email: departmentPeople.email,
            }));

            // Check if recipients array is blank
            if (recipients.length > 0) {
              await EmailNotification.create({
                emailCategory: "SHIPMENT",
                emailType: "SHIPMENT_ESCALATION",
                reciepient: recipients,
                cc: [],
                emailPayload: {
                  orderName: shipment.order.name,
                  distributorName: distributor.name,
                  date: shipment.order.createdAt,
                  department: escalationDepartment.department,
                  shipmentName: shipment.name,
                  shipmentRef: shipment.ref,
                  lastStatus: statusDetails.status,
                  lastStatusChangeDate: latestStatusChange.createdAt,
                },
              });
            }
          }
        }
      } else {
        console.log(`Shipment ${shipment.name} has no status change history.`);
      }
    }
  } catch (error) {
    console.log(error);
  }
};

export const checkSLABreach = async () => {
  try {
    const currentDate = new Date();

    const shipments = await Shipment.find({
      sla: { $exists: true },
      "sla.statusEnded": false,
    });

    if (!shipments || shipments.length === 0) {
      console.log("No shipments found with slaStatus breach.");
      return;
    }

    for (let i = 0; i < shipments.length; i++) {
      const shipment = shipments[i];

      if (shipment.sla.createdAt && shipment.sla.days) {
        const slaCreatedDate = new Date(shipment.sla.createdAt);
        slaCreatedDate.setDate(slaCreatedDate.getDate() + shipment.sla.days);

        if (currentDate > slaCreatedDate) {
          const delayDays = Math.floor(
            (currentDate - slaCreatedDate) / (1000 * 60 * 60 * 24)
          );

          const statusDetails = await Status.findById(shipment.status);
          const escalationDepartment = statusDetails.escalationDepartment;

          const departmentPeoples = await Department.find({
            departmentType: escalationDepartment._id,
          });

          const departmentDetails = await getDepartmentForCreated(
            statusDetails._id
          );

          const distributerId = shipment.order.distributor._id;
          const distributor = await Distributor.findOne({ _id: distributerId });

          const recipients = departmentPeoples.map((departmentPeople) => ({
            name: departmentPeople.name,
            email: departmentPeople.email,
            cc: [...departmentDetails],
          }));

          if (recipients.length > 0) {
            await EmailNotification.create({
              emailCategory: "SHIPMENT",
              emailType: "SLA_BREACH",
              reciepient: recipients,
              emailPayload: {
                orderName: shipment.order.name,
                distributorName: distributor.name,
                shipmentName: shipment.name,
                fulfillmentDays: shipment.sla.days,
                delayDays: delayDays,
              },
            });
          }

          console.log(
            `SLA Breach! Shipment ID: ${shipment._id}, SLA Deadline: ${slaCreatedDate}`
          );
        }
      }
    }
  } catch (error) {
    console.error("Error checking SLA breach:", error);
  }
};
